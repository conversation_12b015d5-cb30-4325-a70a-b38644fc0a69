# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
.DS_Store
dist
dist-ssr
coverage
public
*.local

/cypress/videos/
/cypress/screenshots/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

*.tsbuildinfo

# wrangler files
.wrangler
.dev.vars*

.vectorcode/


# Project specific
backend/support/db_data/
backend/support/redis_data/
backend/support/pgadmin/
backend/support/venv/
backend/support/logs/
backend/support/*.log
backend/support/backups/
backend/support/token
backend/support/env.conf
backend/support/secrets.env
backend/django_auth/static/
backend/django_auth/authentication/migrations/
backend/main/migrations/versions/

*/env.conf
*/secrets.env
*/token
# Python
__pycache__/
*.py[cod]
*.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
ENV/
env/
.env

# IDE
.idea/
.vscode/
*.swp
*.swo
*~

# Logs
*.log
log/
logs/

# Database
*.sqlite3
*.db

# System Files
.DS_Store
Thumbs.db

# Alembic
versions/
.aider*
.vscode/
memory-bank/
.clinerules
.zed/
.venv/
pyrightconfig.json

pyproject.toml
.requirements.txt
.vectorcode/
.deploy-support/
fotos/
.trash/

*/gnrs-go
