# GNRS Go Backend

This is a Go-based backend implementation that replaces the existing Python FastAPI + Django backend while maintaining full API compatibility.

## Architecture

The Go backend follows the migration plan specifications and includes:

- **Gin** web framework for HTTP handling
- **GORM** for database operations with PostgreSQL
- **Redis** for caching and rate limiting
- **JWT** authentication with API key support
- **File upload** handling for photos
- **Wilayah API** proxy for Indonesian region data

## Project Structure

```
new-backend/
├── cmd/api/                 # Application entry point
├── internal/
│   ├── auth/               # Authentication (JWT, API keys)
│   ├── cache/              # Redis client and caching
│   ├── clients/            # External API clients (Wilayah)
│   ├── config/             # Configuration management
│   ├── db/                 # Database connection and setup
│   ├── files/              # File handling (photos)
│   ├── http/
│   │   ├── handlers/       # HTTP request handlers
│   │   ├── middleware/     # HTTP middleware
│   │   ├── responses/      # Response helpers
│   │   └── routes/         # Route definitions
│   ├── models/             # Database models and DTOs
│   └── services/           # Business logic services
├── migrations/             # Database migrations
├── scripts/                # Build and run scripts
└── fotos/                  # Photo storage directory
```

## API Endpoints

The Go backend maintains full compatibility with the existing Python backend:

### Health
- `GET /` - API status check
- `GET /health` - Health check

### Authentication
- `POST /auth/login` - User login
- `POST /auth/verify` - Token verification
- `POST /auth/refresh` - Token refresh
- `POST /auth/apikeys` - Create API key (authenticated)

### URL Shortener
- `GET /url/{code}` - Get original URL
- `POST /url` - Create shortened URL (authenticated, rate limited)

### Wilayah (Indonesian Regions)
- `GET /wilayah/provinces` - Get provinces
- `GET /wilayah/regencies/{province_code}` - Get regencies
- `GET /wilayah/districts/{regency_code}` - Get districts
- `GET /wilayah/villages/{district_code}` - Get villages
- `GET /wilayah/{path}` - Proxy to upstream API

### Data Endpoints
- `GET /data/hobi` - Get hobby data
- `GET /data/daerah/{daerah}` - Get region data
- `GET /data/kelas-sekolah` - Get school class data
- `GET /data/materi/{kategori}` - Get material data
- `GET /data/materi/{kategori}/{detail_kategori}` - Get detailed material data

### Biodata Generus
- `GET /biodata/generus?daerah={daerah}` - List biodata (authenticated)
- `GET /biodata/generus/{biodata_id}` - Get biodata details (authenticated)
- `POST /biodata/generus` - Create biodata with photo upload (authenticated)
- `GET /biodata/generus/foto/{filename}` - Serve photo files (authenticated)

## Configuration

The application uses environment variables for configuration:

### Server
- `PORT` - Server port (default: 8000)
- `ENVIRONMENT` - Environment mode (default: development)

### Database
- `POSTGRES_CONTAINER_NAME` - PostgreSQL host (default: localhost)
- `POSTGRES_USER` - Database user (default: gnrs_user)
- `POSTGRES_PASSWORD` - Database password
- `POSTGRES_DB` - Database name (default: gnrs_db)
- `POSTGRES_PORT` - Database port (default: 5432)

### Redis
- `REDIS_CONTAINER_NAME` - Redis host (default: localhost)
- `REDIS_PORT` - Redis port (default: 6379)

### Authentication
- `JWT_SECRET` - JWT signing secret
- `JWT_ACCESS_TOKEN_TTL` - Access token TTL in seconds (default: 900)
- `JWT_REFRESH_TOKEN_TTL` - Refresh token TTL in seconds (default: 4500)

### CORS
- `CORS_ALLOWED_ORIGINS` - Comma-separated list of allowed origins

### File Upload
- `MAX_FILE_SIZE` - Maximum file size in bytes (default: 2097152 = 2MB)
- `FOTOS_DIR` - Photo storage directory (default: fotos)

## Running the Application

### Prerequisites
- Go 1.21 or later
- PostgreSQL database
- Redis server

### Development
```bash
# Install dependencies
go mod download

# Run the application
./scripts/run.sh
```

### Production Build
```bash
# Build the application
./scripts/build.sh

# Run the built binary
./gnrs-go
```

## Database Models

The Go backend uses GORM models that map to the existing database tables:

- `URL` → `rec_shorten_urls`
- `BiodataGenerus` → `data_biodata_generus`
- `DataHobi` → `data_hobi`
- `DataDaerah` → `data_daerah`
- `DataKelasSekolah` → `data_kelas_sekolah`
- `DataMateri` → `data_materi`
- `APIKey` → `authentication_apikey`
- `User` → `auth_user`

## Authentication

The backend supports two authentication methods:

1. **JWT Tokens** - Bearer tokens for user authentication
   - `Authorization: Bearer <token>`
   - Provides full read/write permissions

2. **API Keys** - For service-to-service authentication
   - `Authorization: ApiKey <key>`
   - Configurable permissions (read_only, write_only, read_write)
   - Optional endpoint restrictions

## Rate Limiting

Rate limiting is implemented using Redis:
- URL creation: 10 requests per minute per IP
- Configurable per-endpoint limits
- Headers included: X-RateLimit-Limit, X-RateLimit-Remaining, X-RateLimit-Reset

## File Handling

Photo uploads are handled with:
- PNG format validation
- 2MB size limit
- Filename format: `{biodata_id}_{sanitized_name}_{date}_{foto_id}.png`
- Storage in configurable directory

## Migration from Python Backend

This Go backend is designed as a drop-in replacement for the existing Python backend:

1. **Database Compatibility** - Uses existing tables and schemas
2. **API Compatibility** - Maintains exact endpoint paths and response formats
3. **Authentication Compatibility** - Supports existing JWT secrets and API keys
4. **CORS Compatibility** - Uses same allowed origins
5. **File Compatibility** - Uses same photo storage format

## Development Notes

- The application uses structured logging with Zap
- GORM auto-migration creates tables if they don't exist
- Redis is used for caching, rate limiting, and session storage
- Error responses maintain compatibility with the Python backend
- All endpoints include proper error handling and validation
