// Package auth provides authentication and authorization services
package auth

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"log"
	"slices"
	"time"

	"gnrs-go/internal/cache"
	"gnrs-go/internal/config"
	"gnrs-go/internal/db"
	"gnrs-go/internal/models"

	"gorm.io/gorm"
)

// Service handles authentication operations
type Service struct {
	db         *db.DB
	cache      *cache.Client
	jwtManager *JWTManager
	cfg        *config.Config
}

// NewService creates a new authentication service
func NewService(database *db.DB, cacheClient *cache.Client, cfg *config.Config) *Service {
	return &Service{
		db:         database,
		cache:      cacheClient,
		jwtManager: NewJWTManager(cfg),
		cfg:        cfg,
	}
}

// GetDB returns the database instance (for middleware use)
func (s *Service) GetDB() *db.DB {
	return s.db
}

// Login authenticates a user and returns JWT tokens
func (s *Service) Login(ctx context.Context, req *models.LoginRequest) (*models.LoginResponse, error) {
	// Find user by username
	var user models.User
	if err := s.db.Where("username = ? AND is_active = ?", req.Username, true).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("invalid credentials")
		}
		return nil, fmt.Errorf("database error: %w", err)
	}

	// Verify password
	if !user.CheckPassword(req.Password) {
		return nil, fmt.Errorf("invalid credentials")
	}

	// Check if user is active
	if !user.IsActive {
		return nil, fmt.Errorf("user account is inactive")
	}

	// Update last login
	now := time.Now()
	user.LastLogin = &now
	if err := s.db.Save(&user).Error; err != nil {
		log.Printf("warning: failed to update last login for user %d: %v", user.ID, err)
	}

	// Generate tokens
	tokens, err := s.jwtManager.GenerateTokens(&user)
	if err != nil {
		return nil, fmt.Errorf("failed to generate tokens: %w", err)
	}

	// Cache the user for faster subsequent verifications
	cacheKey := fmt.Sprintf("user:%d", user.ID)
	if err := s.cache.Set(ctx, cacheKey, user, 5*time.Minute); err != nil {
		// Non-fatal: proceed even if cache write fails
		log.Printf("warning: cache set failed for %s: %v", cacheKey, err)
	}

	return tokens, nil
}

// VerifyToken verifies a JWT token and returns user information
func (s *Service) VerifyToken(ctx context.Context, tokenString string) (*models.AuthResult, error) {
	// Verify the token
	claims, err := s.jwtManager.VerifyToken(tokenString)
	if err != nil {
		return &models.AuthResult{
			Valid: false,
			Error: err.Error(),
		}, nil
	}

	// Check cache first
	cacheKey := fmt.Sprintf("user:%d", claims.UserID)
	var user models.User
	if err := s.cache.Get(ctx, cacheKey, &user); err != nil {
		// Cache miss, fetch from database
		if err := s.db.Where("id = ? AND is_active = ?", claims.UserID, true).First(&user).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return &models.AuthResult{
					Valid: false,
					Error: "user not found",
				}, nil
			}
			return &models.AuthResult{
				Valid: false,
				Error: "database error",
			}, nil
		}

		// Cache the user
		if err := s.cache.Set(ctx, cacheKey, user, 5*time.Minute); err != nil {
			log.Printf("warning: cache set failed for %s: %v", cacheKey, err)
		}
	}

	return &models.AuthResult{
		Valid:      true,
		UserID:     &user.ID,
		Username:   &user.Username,
		Permission: "read_write", // Bearer tokens get full permissions
	}, nil
}

// VerifyAPIKey verifies an API key and returns authentication information
func (s *Service) VerifyAPIKey(ctx context.Context, apiKey string) (*models.AuthResult, error) {
	// Hash the provided key
	hashedKey := hashAPIKey(apiKey)

	// Check cache first
	cacheKey := fmt.Sprintf("apikey:%s", hashedKey)
	var cachedResult models.AuthResult
	if err := s.cache.Get(ctx, cacheKey, &cachedResult); err == nil {
		return &cachedResult, nil
	}

	// Find API key in database
	var key models.APIKey
	if err := s.db.Preload("Owner").Where("hashed_key = ? AND revoked = ?", hashedKey, false).First(&key).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			result := &models.AuthResult{
				Valid: false,
				Error: "invalid API key",
			}
			// Cache negative result for a short time
			if err := s.cache.Set(ctx, cacheKey, result, 1*time.Minute); err != nil {
				log.Printf("warning: cache set failed for %s: %v", cacheKey, err)
			}
			return result, nil
		}
		return &models.AuthResult{
			Valid: false,
			Error: "database error",
		}, nil
	}

	// Check if key is expired
	if key.IsExpired() {
		result := &models.AuthResult{
			Valid: false,
			Error: "API key has expired",
		}
		if err := s.cache.Set(ctx, cacheKey, result, 1*time.Minute); err != nil {
			log.Printf("warning: cache set failed for %s: %v", cacheKey, err)
		}
		return result, nil
	}

	// Check if owner is active
	if !key.Owner.IsActive {
		result := &models.AuthResult{
			Valid: false,
			Error: "API key owner is inactive",
		}
		if err := s.cache.Set(ctx, cacheKey, result, 1*time.Minute); err != nil {
			log.Printf("warning: cache set failed for %s: %v", cacheKey, err)
		}
		return result, nil
	}

	// Mark key as used if not already
	if !key.Used {
		key.Used = true
		if err := s.db.Save(&key).Error; err != nil {
			log.Printf("warning: failed to mark API key used (id=%d): %v", key.ID, err)
		}
	}

	result := &models.AuthResult{
		Valid:            true,
		UserID:           &key.Owner.ID,
		Username:         &key.Owner.Username,
		Permission:       key.Permission,
		AllowedEndpoints: key.AllowedEndpoints,
	}

	// Cache the result
	if err := s.cache.Set(ctx, cacheKey, result, 5*time.Minute); err != nil {
		log.Printf("warning: cache set failed for %s: %v", cacheKey, err)
	}

	return result, nil
}

// RefreshToken generates a new access token from a refresh token
func (s *Service) RefreshToken(ctx context.Context, req *models.RefreshRequest) (*models.LoginResponse, error) {
	// Verify the refresh token
	claims, err := s.jwtManager.VerifyToken(req.RefreshToken)
	if err != nil {
		return nil, fmt.Errorf("invalid refresh token: %w", err)
	}

	if claims.TokenType != "refresh" {
		return nil, fmt.Errorf("token is not a refresh token")
	}

	// Get user from database
	var user models.User
	if err := s.db.Where("id = ? AND is_active = ?", claims.UserID, true).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("user not found")
		}
		return nil, fmt.Errorf("database error: %w", err)
	}

	// Generate new tokens
	return s.jwtManager.RefreshToken(req.RefreshToken, &user)
}

// CreateAPIKey creates a new API key for a user
func (s *Service) CreateAPIKey(ctx context.Context, userID uint, req *models.APIKeyCreateRequest) (*models.APIKeyResponse, error) {
	// Validate permission
	validPermissions := []string{"read_only", "write_only", "read_write"}
	permission := req.Permission
	if permission == "" {
		permission = "read_write"
	}

	if !slices.Contains(validPermissions, permission) {
		return nil, fmt.Errorf("invalid permission: %s", permission)
	}

	// Create API key
	apiKey := &models.APIKey{
		OwnerID:          userID,
		Name:             req.Name,
		Permission:       permission,
		AllowedEndpoints: req.AllowedEndpoints,
	}

	// Set expiration if provided
	if req.ExpiresInDays != nil && *req.ExpiresInDays > 0 {
		expiresAt := time.Now().AddDate(0, 0, *req.ExpiresInDays)
		apiKey.ExpiresAt = &expiresAt
	}

	// Save to database
	if err := s.db.Create(apiKey).Error; err != nil {
		return nil, fmt.Errorf("failed to create API key: %w", err)
	}

	return &models.APIKeyResponse{
		ID:               apiKey.ID,
		Name:             apiKey.Name,
		Key:              apiKey.Key, // Include key only when creating
		Permission:       apiKey.Permission,
		AllowedEndpoints: apiKey.AllowedEndpoints,
		CreatedAt:        apiKey.CreatedAt,
		ExpiresAt:        apiKey.ExpiresAt,
		Revoked:          apiKey.Revoked,
		Used:             apiKey.Used,
	}, nil
}

// ListAPIKeys returns all API keys for a user
func (s *Service) ListAPIKeys(ctx context.Context, userID uint) ([]*models.APIKeyResponse, error) {
	var apiKeys []models.APIKey
	if err := s.db.Where("owner_id = ?", userID).Find(&apiKeys).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch API keys: %w", err)
	}

	responses := make([]*models.APIKeyResponse, len(apiKeys))
	for i, key := range apiKeys {
		responses[i] = &models.APIKeyResponse{
			ID:               key.ID,
			Name:             key.Name,
			// Don't include the actual key in list responses for security
			Permission:       key.Permission,
			AllowedEndpoints: key.AllowedEndpoints,
			CreatedAt:        key.CreatedAt,
			ExpiresAt:        key.ExpiresAt,
			Revoked:          key.Revoked,
			Used:             key.Used,
		}
	}

	return responses, nil
}

// RevokeAPIKey revokes an API key for a user
func (s *Service) RevokeAPIKey(ctx context.Context, userID uint, keyID uint) error {
	// Find the API key and ensure it belongs to the user
	var apiKey models.APIKey
	if err := s.db.Where("id = ? AND owner_id = ?", keyID, userID).First(&apiKey).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("API key not found")
		}
		return fmt.Errorf("database error: %w", err)
	}

	// Mark as revoked
	apiKey.Revoked = true
	if err := s.db.Save(&apiKey).Error; err != nil {
		return fmt.Errorf("failed to revoke API key: %w", err)
	}

	// Clear from cache
	hashedKey := hashAPIKey(apiKey.Key)
	cacheKey := fmt.Sprintf("apikey:%s", hashedKey)
	if err := s.cache.Delete(ctx, cacheKey); err != nil {
		log.Printf("warning: failed to clear cache for revoked API key: %v", err)
	}

	return nil
}

// GetAllAPIKeys returns all API keys in the system (admin only)
func (s *Service) GetAllAPIKeys(ctx context.Context) ([]*models.APIKeyResponse, error) {
	var apiKeys []models.APIKey
	if err := s.db.Preload("Owner").Find(&apiKeys).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch API keys: %w", err)
	}

	responses := make([]*models.APIKeyResponse, len(apiKeys))
	for i, key := range apiKeys {
		responses[i] = &models.APIKeyResponse{
			ID:               key.ID,
			Name:             key.Name,
			Permission:       key.Permission,
			AllowedEndpoints: key.AllowedEndpoints,
			CreatedAt:        key.CreatedAt,
			ExpiresAt:        key.ExpiresAt,
			Revoked:          key.Revoked,
			Used:             key.Used,
		}
	}

	return responses, nil
}

// AdminRevokeAPIKey revokes any API key (admin only)
func (s *Service) AdminRevokeAPIKey(ctx context.Context, keyID uint) error {
	// Find the API key
	var apiKey models.APIKey
	if err := s.db.Where("id = ?", keyID).First(&apiKey).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("API key not found")
		}
		return fmt.Errorf("database error: %w", err)
	}

	// Mark as revoked
	apiKey.Revoked = true
	if err := s.db.Save(&apiKey).Error; err != nil {
		return fmt.Errorf("failed to revoke API key: %w", err)
	}

	// Clear from cache
	hashedKey := hashAPIKey(apiKey.Key)
	cacheKey := fmt.Sprintf("apikey:%s", hashedKey)
	if err := s.cache.Delete(ctx, cacheKey); err != nil {
		log.Printf("warning: failed to clear cache for revoked API key: %v", err)
	}

	return nil
}

// RegisterUser creates a new user account
func (s *Service) RegisterUser(ctx context.Context, req *models.RegisterRequest) (*models.UserResponse, error) {
	// Check if username already exists
	var existingUser models.User
	if err := s.db.Where("username = ?", req.Username).First(&existingUser).Error; err == nil {
		return nil, fmt.Errorf("username already exists")
	} else if err != gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("database error: %w", err)
	}

	// Check if this is the first user (bootstrap case)
	var userCount int64
	if err := s.db.Model(&models.User{}).Count(&userCount).Error; err != nil {
		return nil, fmt.Errorf("failed to count users: %w", err)
	}

	// Create new user
	user := &models.User{
		Username: req.Username,
		IsActive: userCount == 0, // First user is automatically active
	}

	// Set admin privileges for first user or if role is admin
	if userCount == 0 || req.Role == "admin" {
		user.IsStaff = true
		user.IsSuperuser = true
		user.IsActive = true // Admin users are automatically active
	}

	// Hash and set password
	if err := user.SetPassword(req.Password); err != nil {
		return nil, fmt.Errorf("failed to set password: %w", err)
	}

	// Save user to database
	if err := s.db.Create(user).Error; err != nil {
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	return user.ToUserResponse(), nil
}

// GetAllUsers returns all users (admin only)
func (s *Service) GetAllUsers(ctx context.Context) ([]*models.UserResponse, error) {
	var users []models.User
	if err := s.db.Find(&users).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch users: %w", err)
	}

	responses := make([]*models.UserResponse, len(users))
	for i, user := range users {
		responses[i] = user.ToUserResponse()
	}

	return responses, nil
}

// ActivateUser activates a user account
func (s *Service) ActivateUser(ctx context.Context, userID uint) error {
	result := s.db.Model(&models.User{}).Where("id = ?", userID).Update("is_active", true)
	if result.Error != nil {
		return fmt.Errorf("failed to activate user: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("user not found")
	}

	// Clear cache for this user
	cacheKey := fmt.Sprintf("user:%d", userID)
	if err := s.cache.Delete(ctx, cacheKey); err != nil {
		log.Printf("warning: failed to clear cache for user %d: %v", userID, err)
	}

	return nil
}

// DeactivateUser deactivates a user account
func (s *Service) DeactivateUser(ctx context.Context, userID uint) error {
	result := s.db.Model(&models.User{}).Where("id = ?", userID).Update("is_active", false)
	if result.Error != nil {
		return fmt.Errorf("failed to deactivate user: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("user not found")
	}

	// Clear cache for this user
	cacheKey := fmt.Sprintf("user:%d", userID)
	if err := s.cache.Delete(ctx, cacheKey); err != nil {
		log.Printf("warning: failed to clear cache for user %d: %v", userID, err)
	}

	return nil
}

// GrantAdminPrivileges grants admin privileges to a user
func (s *Service) GrantAdminPrivileges(ctx context.Context, userID uint) error {
	result := s.db.Model(&models.User{}).Where("id = ?", userID).Updates(map[string]interface{}{
		"is_staff":     true,
		"is_superuser": true,
	})
	if result.Error != nil {
		return fmt.Errorf("failed to grant admin privileges: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("user not found")
	}

	// Clear cache for this user
	cacheKey := fmt.Sprintf("user:%d", userID)
	if err := s.cache.Delete(ctx, cacheKey); err != nil {
		log.Printf("warning: failed to clear cache for user %d: %v", userID, err)
	}

	return nil
}

// RevokeAdminPrivileges revokes admin privileges from a user
func (s *Service) RevokeAdminPrivileges(ctx context.Context, userID uint) error {
	result := s.db.Model(&models.User{}).Where("id = ?", userID).Updates(map[string]interface{}{
		"is_staff":     false,
		"is_superuser": false,
	})
	if result.Error != nil {
		return fmt.Errorf("failed to revoke admin privileges: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("user not found")
	}

	// Clear cache for this user
	cacheKey := fmt.Sprintf("user:%d", userID)
	if err := s.cache.Delete(ctx, cacheKey); err != nil {
		log.Printf("warning: failed to clear cache for user %d: %v", userID, err)
	}

	return nil
}

// DeleteUser deletes a user account
func (s *Service) DeleteUser(ctx context.Context, userID uint) error {
	result := s.db.Delete(&models.User{}, userID)
	if result.Error != nil {
		return fmt.Errorf("failed to delete user: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("user not found")
	}

	// Clear cache for this user
	cacheKey := fmt.Sprintf("user:%d", userID)
	if err := s.cache.Delete(ctx, cacheKey); err != nil {
		log.Printf("warning: failed to clear cache for user %d: %v", userID, err)
	}

	return nil
}

// GetUserByUsername finds a user by username
func (s *Service) GetUserByUsername(ctx context.Context, username string) (*models.User, error) {
	var user models.User
	if err := s.db.Where("username = ?", username).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("user not found")
		}
		return nil, fmt.Errorf("database error: %w", err)
	}
	return &user, nil
}

// ResetUserPassword resets a user's password
func (s *Service) ResetUserPassword(ctx context.Context, userID uint, newPassword string) error {
	var user models.User
	if err := s.db.First(&user, userID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("user not found")
		}
		return fmt.Errorf("database error: %w", err)
	}

	if err := user.SetPassword(newPassword); err != nil {
		return fmt.Errorf("failed to set password: %w", err)
	}

	if err := s.db.Save(&user).Error; err != nil {
		return fmt.Errorf("failed to update password: %w", err)
	}

	// Clear cache for this user
	cacheKey := fmt.Sprintf("user:%d", userID)
	if err := s.cache.Delete(ctx, cacheKey); err != nil {
		log.Printf("warning: failed to clear cache for user %d: %v", userID, err)
	}

	return nil
}

// hashAPIKey creates a SHA256 hash of an API key
func hashAPIKey(key string) string {
	hash := sha256.Sum256([]byte(key))
	return hex.EncodeToString(hash[:])
}
