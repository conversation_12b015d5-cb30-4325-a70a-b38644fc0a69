// Package config provides configuration for the application
package config

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"strings"

	"github.com/spf13/viper"
)

// Config holds all configuration for the application
type Config struct {
	// Server configuration
	Port        int    `mapstructure:"PORT"`
	Environment string `mapstructure:"ENVIRONMENT"`

	// Database configuration
	PostgresHost     string `mapstructure:"POSTGRES_CONTAINER_NAME"`
	PostgresUser     string `mapstructure:"POSTGRES_USER"`
	PostgresPassword string `mapstructure:"POSTGRES_PASSWORD"`
	PostgresDB       string `mapstructure:"POSTGRES_DB"`
	PostgresPort     string `mapstructure:"POSTGRES_PORT"`
	PoolSize         int    `mapstructure:"POOL_SIZE"`
	MaxOverflow      int    `mapstructure:"MAX_OVERFLOW"`
	PoolTimeout      int    `mapstructure:"POOL_TIMEOUT"`
	PoolRecycle      int    `mapstructure:"POOL_RECYCLE"`
	ConnectTimeout   int    `mapstructure:"CONNECT_TIMEOUT"`

	// Redis configuration
	RedisHost string `mapstructure:"REDIS_CONTAINER_NAME"`
	RedisPort string `mapstructure:"REDIS_PORT"`

	// JWT configuration
	JWTSecret          string `mapstructure:"JWT_SECRET"`
	JWTAccessTokenTTL  int    `mapstructure:"JWT_ACCESS_TOKEN_TTL"`
	JWTRefreshTokenTTL int    `mapstructure:"JWT_REFRESH_TOKEN_TTL"`

	// Django configuration (for compatibility during migration)
	DjangoSecretKey string `mapstructure:"DJANGO_SECRET_KEY"`

	// CORS configuration
	CORSAllowedOrigins []string `mapstructure:"CORS_ALLOWED_ORIGINS"`

	// File upload configuration
	MaxFileSize int64  `mapstructure:"MAX_FILE_SIZE"`
	FotosDir    string `mapstructure:"FOTOS_DIR"`
}

// Load loads configuration from environment variables and config files
func Load() (*Config, error) {
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath(".")
	viper.AddConfigPath("./config")

	// Set defaults
	setDefaults()

	// Read config file if it exists
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, err
		}
	}

	// Override with environment variables
	viper.AutomaticEnv()

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, err
	}

	// Parse CORS origins from comma-separated string
	if corsOrigins := viper.GetString("CORS_ALLOWED_ORIGINS"); corsOrigins != "" {
		config.CORSAllowedOrigins = strings.Split(corsOrigins, ",")
		for i, origin := range config.CORSAllowedOrigins {
			config.CORSAllowedOrigins[i] = strings.TrimSpace(origin)
		}
	}

	return &config, nil
}

// setDefaults sets default configuration values
func setDefaults() {
	// Server defaults
	viper.SetDefault("PORT", 8000)
	viper.SetDefault("ENVIRONMENT", "development")

	// Database defaults (matching Python backend)
	viper.SetDefault("POSTGRES_CONTAINER_NAME", "localhost")
	viper.SetDefault("POSTGRES_USER", "database_user")
	// Generate a random 32-character hex string for the password if not provided
	password, err := generateRandomString(16)
	if err != nil {
		// Fallback to a hardcoded password on error, e.g. if /dev/urandom is not available
		password = "fallback-super-secret-password-change-me"
	}
	viper.SetDefault("POSTGRES_PASSWORD", password)
	viper.SetDefault("POSTGRES_DB", "database_db")
	viper.SetDefault("POSTGRES_PORT", "5432")
	viper.SetDefault("POOL_SIZE", 5)
	viper.SetDefault("MAX_OVERFLOW", 10)
	viper.SetDefault("POOL_TIMEOUT", 30)
	viper.SetDefault("POOL_RECYCLE", 1800)
	viper.SetDefault("CONNECT_TIMEOUT", 10)

	// Redis defaults
	viper.SetDefault("REDIS_CONTAINER_NAME", "localhost")
	viper.SetDefault("REDIS_PORT", "6379")

	// JWT defaults (matching SimpleJWT settings)
	jwtSecret, err := generateRandomString(16)
	if err != nil {
		jwtSecret = "fallback-super-secret-jwt-secret-change-me"
	}
	viper.SetDefault("JWT_SECRET", jwtSecret)
	viper.SetDefault("JWT_ACCESS_TOKEN_TTL", 900)   // 15 minutes
	viper.SetDefault("JWT_REFRESH_TOKEN_TTL", 4500) // 75 minutes

	// Django compatibility
	djangoSecret, err := generateRandomString(16)
	if err != nil {
		djangoSecret = "fallback-super-secret-django-key-change-me"
	}
	viper.SetDefault("DJANGO_SECRET_KEY", djangoSecret)

	// CORS defaults (matching Python backend)
	viper.SetDefault("CORS_ALLOWED_ORIGINS", "https://ngaji.brkh.work,https://29.brkh.work,https://hj.brkh.work,https://hd.brkh.work")

	// File upload defaults
	viper.SetDefault("MAX_FILE_SIZE", 2097152) // 2MB in bytes
	viper.SetDefault("FOTOS_DIR", "fotos")
}

// GetDatabaseDSN returns the PostgreSQL connection string
func (c *Config) GetDatabaseDSN() string {
	return fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s sslmode=disable TimeZone=UTC",
		c.PostgresHost, c.PostgresUser, c.PostgresPassword, c.PostgresDB, c.PostgresPort)
}

// GetRedisAddr returns the Redis connection address
func (c *Config) GetRedisAddr() string {
	return fmt.Sprintf("%s:%s", c.RedisHost, c.RedisPort)
}

// generateRandomString generates a random hex-encoded string.
// n is the number of bytes of randomness. The resulting string will be 2n characters long.
func generateRandomString(n int) (string, error) {
	bytes := make([]byte, n)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}
