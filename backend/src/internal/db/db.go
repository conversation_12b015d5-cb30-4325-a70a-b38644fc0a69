// Package db provides database utilities and thin wrappers around GORM for common operations like migrations and health checks.
package db

import (
	"fmt"
	"time"

	"gnrs-go/internal/config"
	"gnrs-go/internal/models"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// DB wraps the GORM database instance
type DB struct {
	*gorm.DB
}

// Initialize creates and configures the database connection
func Initialize(cfg *config.Config) (*DB, error) {
	dsn := cfg.GetDatabaseDSN()

	// Configure GORM logger
	var gormLogger logger.Interface
	if cfg.Environment == "production" {
		gormLogger = logger.Default.LogMode(logger.Silent)
	} else {
		gormLogger = logger.Default.LogMode(logger.Info)
	}

	// Open database connection
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: gormLogger,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// Get underlying sql.DB to configure connection pool
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	// Configure connection pool (matching Python backend settings)
	sqlDB.SetMaxIdleConns(cfg.PoolSize)
	sqlDB.SetMaxOpenConns(cfg.PoolSize + cfg.MaxOverflow)
	sqlDB.SetConnMaxLifetime(time.Duration(cfg.PoolRecycle) * time.Second)
	sqlDB.SetConnMaxIdleTime(time.Duration(cfg.PoolTimeout) * time.Second)

	// Test the connection
	if err := sqlDB.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	// Auto-migrate models (equivalent to SQLModel.metadata.create_all)
	if err := autoMigrate(db); err != nil {
		return nil, fmt.Errorf("failed to auto-migrate: %w", err)
	}

	return &DB{db}, nil
}

// autoMigrate runs GORM auto-migration for all models
func autoMigrate(db *gorm.DB) error {
	return db.AutoMigrate(
		&models.URL{},
		&models.BiodataGenerus{},
		&models.DataHobi{},
		&models.DataDaerah{},
		&models.DataKelasSekolah{},
		&models.DataMateri{},
		&models.APIKey{},
		&models.User{},
	)
}

// Close closes the database connection
func (db *DB) Close() error {
	sqlDB, err := db.DB.DB()
	if err != nil {
		return err
	}
	return sqlDB.Close()
}

// Health checks if the database connection is healthy
func (db *DB) Health() error {
	sqlDB, err := db.DB.DB()
	if err != nil {
		return err
	}
	return sqlDB.Ping()
}
