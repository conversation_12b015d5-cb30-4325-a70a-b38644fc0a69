package handlers

import (
	"fmt"
	"net/http"

	"gnrs-go/internal/auth"
	"gnrs-go/internal/http/responses"
	"gnrs-go/internal/models"

	"github.com/gin-gonic/gin"
)

// AuthHandler handles authentication endpoints
type AuthHandler struct {
	authService *auth.Service
}

// NewAuthHandler creates a new auth handler
func NewAuthHandler(authService *auth.Service) *AuthHandler {
	return &AuthHandler{
		authService: authService,
	}
}

// Login handles user login
func (h *AuthHandler) Login(c *gin.Context) {
	var req models.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responses.BadRequest(c, "Invalid request format")
		return
	}

	tokens, err := h.authService.Login(c.Request.Context(), &req)
	if err != nil {
		responses.Unauthorized(c, err.Error())
		return
	}

	c.<PERSON><PERSON>(http.StatusOK, tokens)
}

// Verify handles token verification
func (h *AuthHandler) Verify(c *gin.Context) {
	var req models.VerifyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responses.BadRequest(c, "Invalid request format")
		return
	}

	result, err := h.authService.VerifyToken(c.Request.Context(), req.Token)
	if err != nil {
		responses.ServiceUnavailable(c, "Authentication service error")
		return
	}

	c.JSON(http.StatusOK, result)
}

// Refresh handles token refresh
func (h *AuthHandler) Refresh(c *gin.Context) {
	var req models.RefreshRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responses.BadRequest(c, "Invalid request format")
		return
	}

	tokens, err := h.authService.RefreshToken(c.Request.Context(), &req)
	if err != nil {
		responses.Unauthorized(c, err.Error())
		return
	}

	c.JSON(http.StatusOK, tokens)
}

// CreateAPIKey handles API key creation
func (h *AuthHandler) CreateAPIKey(c *gin.Context) {
	// Get user from auth context
	authResult, exists := c.Get("auth_result")
	if !exists {
		responses.Unauthorized(c, "Authentication required")
		return
	}

	result := authResult.(*models.AuthResult)
	if result.UserID == nil {
		responses.Unauthorized(c, "Invalid authentication")
		return
	}

	var req models.APIKeyCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responses.BadRequest(c, "Invalid request format")
		return
	}

	apiKey, err := h.authService.CreateAPIKey(c.Request.Context(), *result.UserID, &req)
	if err != nil {
		responses.BadRequest(c, err.Error())
		return
	}

	responses.Created(c, apiKey)
}

// Register handles user registration
func (h *AuthHandler) Register(c *gin.Context) {
	var req models.RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responses.BadRequest(c, "Invalid request format")
		return
	}

	user, err := h.authService.RegisterUser(c.Request.Context(), &req)
	if err != nil {
		responses.BadRequest(c, err.Error())
		return
	}

	responses.Created(c, user)
}

// GetAllUsers handles listing all users (admin only)
func (h *AuthHandler) GetAllUsers(c *gin.Context) {
	users, err := h.authService.GetAllUsers(c.Request.Context())
	if err != nil {
		responses.InternalServerError(c, "Failed to fetch users")
		return
	}

	c.JSON(http.StatusOK, users)
}

// ActivateUser handles user activation (admin only)
func (h *AuthHandler) ActivateUser(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		responses.BadRequest(c, "User ID is required")
		return
	}

	// Convert string to uint
	var id uint
	if _, err := fmt.Sscanf(userID, "%d", &id); err != nil {
		responses.BadRequest(c, "Invalid user ID")
		return
	}

	if err := h.authService.ActivateUser(c.Request.Context(), id); err != nil {
		responses.BadRequest(c, err.Error())
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "User activated successfully"})
}

// DeactivateUser handles user deactivation (admin only)
func (h *AuthHandler) DeactivateUser(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		responses.BadRequest(c, "User ID is required")
		return
	}

	// Convert string to uint
	var id uint
	if _, err := fmt.Sscanf(userID, "%d", &id); err != nil {
		responses.BadRequest(c, "Invalid user ID")
		return
	}

	if err := h.authService.DeactivateUser(c.Request.Context(), id); err != nil {
		responses.BadRequest(c, err.Error())
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "User deactivated successfully"})
}

// GrantAdminPrivileges handles granting admin privileges (admin only)
func (h *AuthHandler) GrantAdminPrivileges(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		responses.BadRequest(c, "User ID is required")
		return
	}

	// Convert string to uint
	var id uint
	if _, err := fmt.Sscanf(userID, "%d", &id); err != nil {
		responses.BadRequest(c, "Invalid user ID")
		return
	}

	if err := h.authService.GrantAdminPrivileges(c.Request.Context(), id); err != nil {
		responses.BadRequest(c, err.Error())
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Admin privileges granted successfully"})
}

// RevokeAdminPrivileges handles revoking admin privileges (admin only)
func (h *AuthHandler) RevokeAdminPrivileges(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		responses.BadRequest(c, "User ID is required")
		return
	}

	// Convert string to uint
	var id uint
	if _, err := fmt.Sscanf(userID, "%d", &id); err != nil {
		responses.BadRequest(c, "Invalid user ID")
		return
	}

	if err := h.authService.RevokeAdminPrivileges(c.Request.Context(), id); err != nil {
		responses.BadRequest(c, err.Error())
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Admin privileges revoked successfully"})
}

// DeleteUser handles user deletion (admin only)
func (h *AuthHandler) DeleteUser(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		responses.BadRequest(c, "User ID is required")
		return
	}

	// Convert string to uint
	var id uint
	if _, err := fmt.Sscanf(userID, "%d", &id); err != nil {
		responses.BadRequest(c, "Invalid user ID")
		return
	}

	if err := h.authService.DeleteUser(c.Request.Context(), id); err != nil {
		responses.BadRequest(c, err.Error())
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "User deleted successfully"})
}
