package handlers

import (
	"gnrs-go/internal/clients"
	"gnrs-go/internal/http/responses"

	"github.com/gin-gonic/gin"
)

// WilayahHandler handles wilayah (region) endpoints
type WilayahHandler struct {
	wilayahClient *clients.WilayahClient
}

// NewWilayahHandler creates a new wilayah handler
func NewWilayahHandler(wilayahClient *clients.WilayahClient) *WilayahHandler {
	return &WilayahHandler{
		wilayahClient: wilayahClient,
	}
}

// GetProvinces handles GET /wilayah/provinces
func (h *WilayahHandler) GetProvinces(c *gin.Context) {
	data, err := h.wilayahClient.GetProvinces(c.Request.Context())
	if err != nil {
		responses.BadGateway(c, err.Error())
		return
	}

	responses.WithCORS(c, data)
}

// GetRegencies handles GET /wilayah/regencies/{province_code}
func (h *WilayahHandler) GetRegencies(c *gin.Context) {
	provinceCode := c.<PERSON>m("province_code")
	if provinceCode == "" {
		responses.BadRequest(c, "Province code is required")
		return
	}

	data, err := h.wilayahClient.GetRegencies(c.Request.Context(), provinceCode)
	if err != nil {
		responses.BadGateway(c, err.Error())
		return
	}

	responses.WithCORS(c, data)
}

// GetDistricts handles GET /wilayah/districts/{regency_code}
func (h *WilayahHandler) GetDistricts(c *gin.Context) {
	regencyCode := c.Param("regency_code")
	if regencyCode == "" {
		responses.BadRequest(c, "Regency code is required")
		return
	}

	data, err := h.wilayahClient.GetDistricts(c.Request.Context(), regencyCode)
	if err != nil {
		responses.BadGateway(c, err.Error())
		return
	}

	responses.WithCORS(c, data)
}

// GetVillages handles GET /wilayah/villages/{district_code}
func (h *WilayahHandler) GetVillages(c *gin.Context) {
	districtCode := c.Param("district_code")
	if districtCode == "" {
		responses.BadRequest(c, "District code is required")
		return
	}

	data, err := h.wilayahClient.GetVillages(c.Request.Context(), districtCode)
	if err != nil {
		responses.BadGateway(c, err.Error())
		return
	}

	responses.WithCORS(c, data)
}

// GetWilayahData handles GET /wilayah/{full_code:path} (catch-all)
func (h *WilayahHandler) GetWilayahData(c *gin.Context) {
	fullCode := c.Param("full_code")
	if fullCode == "" {
		responses.BadRequest(c, "Path is required")
		return
	}

	data, err := h.wilayahClient.ProxyRequest(c.Request.Context(), fullCode)
	if err != nil {
		responses.BadGateway(c, err.Error())
		return
	}

	responses.WithCORS(c, data)
}
