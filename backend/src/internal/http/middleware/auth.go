// Package middleware provides HTTP middleware for authentication and authorization
package middleware

import (
	"net/http"
	"strings"

	"gnrs-go/internal/auth"
	"gnrs-go/internal/models"

	"github.com/gin-gonic/gin"
)

// AuthMiddleware creates authentication middleware
func AuthMiddleware(authService *auth.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.<PERSON>eader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "No authorization header provided"})
			c.Abort()
			return
		}

		parts := strings.SplitN(authHeader, " ", 2)
		if len(parts) != 2 {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid authorization header format"})
			c.Abort()
			return
		}

		authType := strings.ToLower(parts[0])
		token := parts[1]

		var authResult *models.AuthResult
		var err error

		switch authType {
		case "bearer":
			authR<PERSON>ult, err = authService.VerifyToken(c.Request.Context(), token)
		case "apikey":
			authResult, err = authService.VerifyAPIKey(c.Request.Context(), token)
		default:
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid authorization type. Use 'Bearer' or 'ApiKey'"})
			c.Abort()
			return
		}

		if err != nil {
			c.JSON(http.StatusServiceUnavailable, gin.H{"error": "Authentication service error"})
			c.Abort()
			return
		}

		if !authResult.Valid {
			c.JSON(http.StatusUnauthorized, gin.H{"error": authResult.Error})
			c.Abort()
			return
		}

		// For API keys, check endpoint restrictions
		if authType == "apikey" && len(authResult.AllowedEndpoints) > 0 {
			requestPath := c.Request.URL.Path
			allowed := false

			for _, endpoint := range authResult.AllowedEndpoints {
				// Support both with and without '/api' prefix
				candidates := []string{
					"/" + strings.TrimPrefix(endpoint, "/"),
					"/api/" + strings.TrimPrefix(endpoint, "/"),
				}

				for _, candidate := range candidates {
					// Normalize paths
					normalizedCandidate := strings.TrimSuffix(candidate, "/")
					normalizedRequest := strings.TrimSuffix(requestPath, "/")

					// Check exact match or subtree
					if normalizedRequest == normalizedCandidate ||
						strings.HasPrefix(normalizedRequest, normalizedCandidate+"/") {
						allowed = true
						break
					}
				}

				if allowed {
					break
				}
			}

			if !allowed {
				c.JSON(http.StatusForbidden, gin.H{
					"error":             "API key not allowed to access this endpoint",
					"allowed_endpoints": authResult.AllowedEndpoints,
				})
				c.Abort()
				return
			}
		}

		// Store auth result in context
		c.Set("auth_result", authResult)
		c.Next()
	}
}

// RequireReadPermission middleware that requires read permission
func RequireReadPermission() gin.HandlerFunc {
	return func(c *gin.Context) {
		authResult, exists := c.Get("auth_result")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
			c.Abort()
			return
		}

		result := authResult.(*models.AuthResult)
		if result.Permission != "read_only" && result.Permission != "read_write" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Insufficient permissions. Read access required."})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireWritePermission middleware that requires write permission
func RequireWritePermission() gin.HandlerFunc {
	return func(c *gin.Context) {
		authResult, exists := c.Get("auth_result")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
			c.Abort()
			return
		}

		result := authResult.(*models.AuthResult)
		if result.Permission != "write_only" && result.Permission != "read_write" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Insufficient permissions. Write access required."})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireSuperAdminPermission middleware that requires super admin privileges
func RequireSuperAdminPermission(authService *auth.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		authResult, exists := c.Get("auth_result")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
			c.Abort()
			return
		}

		result := authResult.(*models.AuthResult)
		if result.UserID == nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid authentication"})
			c.Abort()
			return
		}

		// Get user from database to check super admin status
		var user models.User
		if err := authService.GetDB().Where("id = ?", *result.UserID).First(&user).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to verify user permissions"})
			c.Abort()
			return
		}

		if !user.IsSuperuser {
			c.JSON(http.StatusForbidden, gin.H{"error": "Super admin privileges required"})
			c.Abort()
			return
		}

		// Store user in context for handlers
		c.Set("current_user", &user)
		c.Next()
	}
}
