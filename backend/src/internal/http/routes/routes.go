// Package routes contains all routes and middleware for the API
package routes

import (
	"time"

	"gnrs-go/internal/auth"
	"gnrs-go/internal/cache"
	"gnrs-go/internal/clients"
	"gnrs-go/internal/config"
	"gnrs-go/internal/db"
	"gnrs-go/internal/files"
	"gnrs-go/internal/http/handlers"
	"gnrs-go/internal/http/middleware"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// Setup configures all routes and middleware
func Setup(router *gin.Engine, database *db.DB, redisClient *cache.Client, cfg *config.Config, logger *zap.Logger) {
	// Initialize services
	authService := auth.NewService(database, redisClient, cfg)
	fotoService := files.NewFotoService(cfg)
	wilayahClient := clients.NewWilayahClient()

	// Initialize handlers
	healthHandler := handlers.NewHealthHandler()
	authHandler := handlers.NewAuthHandler(authService)
	urlHandler := handlers.NewURLHandler(database, redisClient)
	wilayahHandler := handlers.NewWilayahHandler(wilayahClient)
	dataHandler := handlers.NewDataHandler(database, redisClient)
	biodataHandler := handlers.NewBiodataHandler(database, redisClient, fotoService)

	// Global middleware
	router.Use(middleware.RequestIDMiddleware())
	router.Use(middleware.LoggingMiddleware(logger))
	router.Use(gin.Recovery())
	router.Use(middleware.CORSMiddleware(cfg))

	// Health endpoints
	router.GET("/", healthHandler.Root)
	router.GET("/health", healthHandler.Health)

	// Authentication endpoints
	authGroup := router.Group("/auth")
	{
		// Public endpoints
		authGroup.POST("/login", authHandler.Login)
		authGroup.POST("/register", authHandler.Register)
		authGroup.POST("/verify", authHandler.Verify)
		authGroup.POST("/refresh", authHandler.Refresh)

		// API key management (requires authentication)
		authGroup.Use(middleware.AuthMiddleware(authService))
		authGroup.GET("/apikeys", authHandler.ListAPIKeys)
		authGroup.POST("/apikeys", authHandler.CreateAPIKey)
		authGroup.DELETE("/apikeys/:id", authHandler.RevokeAPIKey)
	}

	// Admin endpoints (requires super admin privileges)
	adminGroup := router.Group("/admin")
	adminGroup.Use(middleware.AuthMiddleware(authService))
	adminGroup.Use(middleware.RequireSuperAdminPermission(authService))
	{
		adminGroup.GET("/users", authHandler.GetAllUsers)
		adminGroup.PATCH("/users/:id/activate", authHandler.ActivateUser)
		adminGroup.PATCH("/users/:id/deactivate", authHandler.DeactivateUser)
		adminGroup.PATCH("/users/:id/grant-admin", authHandler.GrantAdminPrivileges)
		adminGroup.PATCH("/users/:id/revoke-admin", authHandler.RevokeAdminPrivileges)
		adminGroup.DELETE("/users/:id", authHandler.DeleteUser)

		// API key management (admin)
		adminGroup.GET("/apikeys", authHandler.GetAllAPIKeys)
		adminGroup.DELETE("/apikeys/:id", authHandler.AdminRevokeAPIKey)
	}

	// URL shortener endpoints
	urlGroup := router.Group("/url")
	{
		// GET /url/{code} - no auth required
		urlGroup.GET("/:code", urlHandler.GetURL)

		// POST /url - requires auth and rate limiting
		urlGroup.POST("/",
			middleware.AuthMiddleware(authService),
			middleware.RequireWritePermission(),
			middleware.EndpointBasedRateLimit(redisClient, 10, time.Minute), // 10 requests per minute
			urlHandler.CreateURL,
		)
	}

	// Wilayah (region) endpoints - no auth required
	wilayahGroup := router.Group("/wilayah")
	{
		wilayahGroup.GET("/provinces", wilayahHandler.GetProvinces)
		wilayahGroup.GET("/regencies/:province_code", wilayahHandler.GetRegencies)
		wilayahGroup.GET("/districts/:regency_code", wilayahHandler.GetDistricts)
		wilayahGroup.GET("/villages/:district_code", wilayahHandler.GetVillages)
		// Generic wilayah data lookup - using a specific path to avoid conflicts
		wilayahGroup.GET("/data/*full_code", wilayahHandler.GetWilayahData)
	}

	// Data endpoints - no auth required for read operations
	dataGroup := router.Group("/data")
	{
		dataGroup.GET("/hobi", dataHandler.GetHobiData)
		dataGroup.GET("/daerah/:daerah", dataHandler.GetDaerahData)
		dataGroup.GET("/kelas-sekolah", dataHandler.GetKelasSekolahData)
		dataGroup.GET("/materi/:kategori", dataHandler.GetMateriData)
		dataGroup.GET("/materi/:kategori/:detail_kategori", dataHandler.GetMateriData)
	}

	// Biodata endpoints
	biodataGroup := router.Group("/biodata/generus")
	{
		// Read operations require read permission
		biodataGroup.GET("/",
			middleware.AuthMiddleware(authService),
			middleware.RequireReadPermission(),
			biodataHandler.GetBiodata,
		)

		biodataGroup.GET("/:biodata_id",
			middleware.AuthMiddleware(authService),
			middleware.RequireReadPermission(),
			biodataHandler.GetBiodataByID,
		)

		biodataGroup.GET("/foto/:filename",
			middleware.AuthMiddleware(authService),
			middleware.RequireReadPermission(),
			biodataHandler.ServeFoto,
		)

		// Write operations require write permission
		biodataGroup.POST("/",
			middleware.AuthMiddleware(authService),
			middleware.RequireWritePermission(),
			biodataHandler.CreateBiodata,
		)
	}
}
