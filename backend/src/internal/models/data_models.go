package models

// DataHobi represents the hobi data model
// Maps to table: data_hobi
type DataHobi struct {
	ID       uint   `gorm:"primaryKey;autoIncrement" json:"id"`
	<PERSON><PERSON><PERSON> string `gorm:"index" json:"kate<PERSON>i"`
	Hobi     string `json:"hobi"`
}

// TableName returns the table name for the DataHobi model
func (DataHobi) TableName() string {
	return "data_hobi"
}

// DataDaerah represents the daerah data model
// Maps to table: data_daerah
type DataDaerah struct {
	ID          uint   `gorm:"primaryKey;autoIncrement" json:"id"`
	Daerah      string `gorm:"index" json:"daerah"`
	Ranah       string `json:"ranah"`
	DetailRanah string `json:"detail_ranah"`
}

// TableName returns the table name for the DataDaerah model
func (DataDaerah) TableName() string {
	return "data_daerah"
}

// DataKelasSekolah represents the school class data model
// Maps to table: data_kelas_sekolah
type DataKelasSekolah struct {
	ID       uint   `gorm:"primaryKey;autoIncrement" json:"id"`
	Jenjang  string `json:"jenjang"`
	Ke<PERSON>    string `json:"kelas"`
}

// TableName returns the table name for the DataKelasSekolah model
func (DataKelasSekolah) TableName() string {
	return "data_kelas_sekolah"
}

// DataMateri represents the materi data model
// Maps to table: data_materi
type DataMateri struct {
	ID               uint   `gorm:"primaryKey;autoIncrement" json:"id"`
	Kategori         string `gorm:"index" json:"kategori"`
	DetailKategori   string `gorm:"default:''" json:"detail_kategori"`
	Materi           string `gorm:"default:''" json:"materi"`
	DetailMateri     string `gorm:"default:''" json:"detail_materi"`
	Indikator        string `gorm:"default:''" json:"indikator"`
	IndikatorMulai   string `gorm:"default:''" json:"indikator_mulai"`
	IndikatorAkhir   string `gorm:"default:''" json:"indikator_akhir"`
}

// TableName returns the table name for the DataMateri model
func (DataMateri) TableName() string {
	return "data_materi"
}

// Response structures for API endpoints

// HobiResponse represents the response structure for hobi data
type HobiResponse struct {
	Kategori string `json:"kategori"`
	Hobi     string `json:"hobi"`
}

// DaerahResponse represents the response structure for daerah data
type DaerahResponse struct {
	Ranah       string `json:"ranah"`
	DetailRanah string `json:"detail_ranah"`
}

// KelasSekolahResponse represents the response structure for kelas sekolah data
type KelasSekolahResponse struct {
	Jenjang string `json:"jenjang"`
	Kelas   string `json:"kelas"`
}

// MateriResponse represents the response structure for materi data
type MateriResponse struct {
	Kategori         string `json:"kategori"`
	DetailKategori   string `json:"detail_kategori"`
	Materi           string `json:"materi"`
	DetailMateri     string `json:"detail_materi"`
	Indikator        string `json:"indikator"`
	IndikatorMulai   string `json:"indikator_mulai"`
	IndikatorAkhir   string `json:"indikator_akhir"`
}
