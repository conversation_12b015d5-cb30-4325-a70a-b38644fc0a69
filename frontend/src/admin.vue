<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import {
  login,
  getApi<PERSON>eys,
  generate<PERSON><PERSON><PERSON><PERSON>,
  revoke<PERSON>pi<PERSON><PERSON>,
  getAllUsers,
  activateUser,
  deactivateUser,
  grantAdminPrivileges,
  revokeAdminPrivileges,
  deleteUser,
  type <PERSON><PERSON><PERSON>ey,
  type User
} from "./services/api";
import { useAuthStore } from "./stores/auth";

const auth = useAuthStore();

// Login state
const username = ref("");
const password = ref("");
const loggingIn = ref(false);
const loginError = ref<string | null>(null);

// API keys state
const keys = ref<ApiKey[]>([]);
const loadingKeys = ref(false);
const generating = ref(false);
const newKey = ref<string | null>(null);
const actionError = ref<string | null>(null);

// User management state
const users = ref<User[]>([]);
const loadingUsers = ref(false);
const userActionError = ref<string | null>(null);

const isAuthed = computed(() => auth.isAuthenticated);

// Check if current user is super admin (for user management)
const isSuperAdmin = computed(() => {
  // For now, we'll assume authenticated users have admin privileges
  // In a real app, this would come from the user profile/token
  return isAuthed.value;
});

const doLogin = async () => {
  loginError.value = null;
  actionError.value = null;
  newKey.value = null;
  loggingIn.value = true;
  try {
    const { data } = await login(username.value, password.value);
    if (data?.access_token) {
      auth.setToken(data.access_token);
      await fetchKeys();
      if (isSuperAdmin.value) {
        await fetchUsers();
      }
    } else {
      loginError.value = "Login berhasil tetapi token tidak diterima.";
    }
  } catch (e: any) {
    loginError.value = e?.response?.data?.message || "Login gagal. Periksa kredensial Anda.";
  } finally {
    loggingIn.value = false;
  }
};

const fetchKeys = async () => {
  loadingKeys.value = true;
  actionError.value = null;
  try {
    const { data } = await getApiKeys();
    keys.value = data || [];
  } catch (e: any) {
    actionError.value = e?.response?.data?.message || "Gagal memuat API keys.";
  } finally {
    loadingKeys.value = false;
  }
};

const doGenerate = async () => {
  generating.value = true;
  actionError.value = null;
  try {
    const { data } = await generateApiKey("Admin Generated Key");
    if (data?.key) newKey.value = data.key;
    // Refresh list
    await fetchKeys();
  } catch (e: any) {
    actionError.value = e?.response?.data?.message || "Gagal membuat API key baru.";
  } finally {
    generating.value = false;
  }
};

const doRevoke = async (id: string) => {
  actionError.value = null;
  try {
    await revokeApiKey(id);
    keys.value = keys.value.filter((k) => k.id !== id);
  } catch (e: any) {
    actionError.value = e?.response?.data?.message || "Gagal mencabut API key.";
  }
};

const fetchUsers = async () => {
  loadingUsers.value = true;
  userActionError.value = null;
  try {
    const { data } = await getAllUsers();
    users.value = data;
  } catch (e: any) {
    userActionError.value = e?.response?.data?.message || "Gagal memuat daftar pengguna.";
  } finally {
    loadingUsers.value = false;
  }
};

const handleActivateUser = async (userId: number) => {
  userActionError.value = null;
  try {
    await activateUser(userId);
    await fetchUsers(); // Refresh list
  } catch (e: any) {
    userActionError.value = e?.response?.data?.message || "Gagal mengaktifkan pengguna.";
  }
};

const handleDeactivateUser = async (userId: number) => {
  userActionError.value = null;
  try {
    await deactivateUser(userId);
    await fetchUsers(); // Refresh list
  } catch (e: any) {
    userActionError.value = e?.response?.data?.message || "Gagal menonaktifkan pengguna.";
  }
};

const handleGrantAdmin = async (userId: number) => {
  userActionError.value = null;
  try {
    await grantAdminPrivileges(userId);
    await fetchUsers(); // Refresh list
  } catch (e: any) {
    userActionError.value = e?.response?.data?.message || "Gagal memberikan hak admin.";
  }
};

const handleRevokeAdmin = async (userId: number) => {
  userActionError.value = null;
  try {
    await revokeAdminPrivileges(userId);
    await fetchUsers(); // Refresh list
  } catch (e: any) {
    userActionError.value = e?.response?.data?.message || "Gagal mencabut hak admin.";
  }
};

const handleDeleteUser = async (userId: number) => {
  if (!confirm("Apakah Anda yakin ingin menghapus pengguna ini? Tindakan ini tidak dapat dibatalkan.")) {
    return;
  }

  userActionError.value = null;
  try {
    await deleteUser(userId);
    await fetchUsers(); // Refresh list
  } catch (e: any) {
    userActionError.value = e?.response?.data?.message || "Gagal menghapus pengguna.";
  }
};

const logout = () => {
  auth.clear();
  keys.value = [];
  users.value = [];
  username.value = "";
  password.value = "";
  newKey.value = null;
  userActionError.value = null;
};

onMounted(async () => {
  if (isAuthed.value) {
    await fetchKeys();
    if (isSuperAdmin.value) {
      await fetchUsers();
    }
  }
});
</script>

<template>
  <div class="form-container" style="max-width: 680px;">
    <h1 class="form-title">Admin</h1>

    <!-- Not authenticated: Login form -->
    <div v-if="!isAuthed" style="text-align:left;">
      <div class="form-group">
        <label for="username">Username</label>
        <input id="username" v-model.trim="username" type="text" placeholder="Masukkan username" />
      </div>
      <div class="form-group">
        <label for="password">Password</label>
        <input id="password" v-model="password" type="password" placeholder="Masukkan password" />
      </div>
      <button :disabled="loggingIn" @click="doLogin">
        {{ loggingIn ? "Masuk..." : "Masuk" }}
      </button>
      <div v-if="loginError" class="error-message">{{ loginError }}</div>
    </div>

    <!-- Authenticated: API Key Management -->
    <div v-else>
      <div style="display:flex; justify-content: space-between; align-items:center;">
        <div class="form-subtitle">Manajemen API Keys</div>
        <button class="secondary" @click="logout">Keluar</button>
      </div>

      <div class="spaci"></div>

      <div>
        <button :disabled="generating" @click="doGenerate">
          {{ generating ? "Membuat..." : "Buat API Key Baru" }}
        </button>
        <div v-if="newKey" style="margin-top:10px;">
          <strong>API Key baru:</strong>
          <div style="word-break:break-all;">{{ newKey }}</div>
          <small>Simpan key ini sekarang. Demi keamanan, key mungkin tidak akan ditampilkan lagi.</small>
        </div>
      </div>

      <div class="spaci"></div>

      <div>
        <div class="form-subtitle" style="margin-bottom:10px;">Daftar API Keys</div>
        <div v-if="loadingKeys">Memuat daftar...</div>
        <div v-else>
          <div v-if="keys.length === 0" style="color: var(--color-muted);">Belum ada API key.</div>
          <ul v-else style="list-style:none; padding:0;">
            <li v-for="k in keys" :key="k.id" style="display:flex; align-items:center; justify-content:space-between; padding:10px; border-bottom:1px solid rgba(46,90,53,0.1);">
              <div style="flex:1; min-width:0;">
                <div style="font-weight:600; word-break:break-all;">{{ k.key }}</div>
                <div style="font-size:12px; color: var(--color-muted);">
                  ID: {{ k.id }}
                  <span v-if="k.createdAt"> • {{ new Date(k.createdAt).toLocaleString() }}</span>
                  <span v-if="k.revoked"> • Dicabut</span>
                </div>
              </div>
              <button class="secondary" style="margin-left:10px;" @click="doRevoke(k.id)">Cabut</button>
            </li>
          </ul>
        </div>
      </div>

      <div v-if="actionError" class="error-message">{{ actionError }}</div>

      <!-- User Management Section (Super Admin Only) -->
      <div v-if="isSuperAdmin" class="spaci"></div>
      <div v-if="isSuperAdmin">
        <div class="form-subtitle" style="margin-bottom:10px;">Manajemen Pengguna</div>
        <div v-if="loadingUsers">Memuat daftar pengguna...</div>
        <div v-else>
          <div v-if="users.length === 0" style="color: var(--color-muted);">Belum ada pengguna.</div>
          <div v-else>
            <table style="width: 100%; border-collapse: collapse; margin-top: 10px;">
              <thead>
                <tr style="border-bottom: 2px solid var(--color-primary);">
                  <th style="text-align: left; padding: 8px;">Username</th>
                  <th style="text-align: left; padding: 8px;">Status</th>
                  <th style="text-align: left; padding: 8px;">Role</th>
                  <th style="text-align: left; padding: 8px;">Bergabung</th>
                  <th style="text-align: left; padding: 8px;">Aksi</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="user in users" :key="user.id" style="border-bottom: 1px solid rgba(46,90,53,0.1);">
                  <td style="padding: 8px; font-weight: 600;">{{ user.username }}</td>
                  <td style="padding: 8px;">
                    <span :style="{ color: user.is_active ? 'green' : 'red' }">
                      {{ user.is_active ? 'Aktif' : 'Tidak Aktif' }}
                    </span>
                  </td>
                  <td style="padding: 8px;">
                    <span v-if="user.is_superuser" style="color: var(--color-primary); font-weight: 600;">Super Admin</span>
                    <span v-else-if="user.is_staff" style="color: var(--color-primary);">Admin</span>
                    <span v-else style="color: var(--color-muted);">User</span>
                  </td>
                  <td style="padding: 8px; font-size: 12px; color: var(--color-muted);">
                    {{ new Date(user.date_joined).toLocaleDateString() }}
                  </td>
                  <td style="padding: 8px;">
                    <div style="display: flex; gap: 5px; flex-wrap: wrap;">
                      <button
                        v-if="!user.is_active"
                        @click="handleActivateUser(user.id)"
                        style="font-size: 12px; padding: 4px 8px; background: green; color: white; border: none; border-radius: 4px; cursor: pointer;"
                      >
                        Aktifkan
                      </button>
                      <button
                        v-if="user.is_active"
                        @click="handleDeactivateUser(user.id)"
                        style="font-size: 12px; padding: 4px 8px; background: orange; color: white; border: none; border-radius: 4px; cursor: pointer;"
                      >
                        Nonaktifkan
                      </button>
                      <button
                        v-if="!user.is_superuser"
                        @click="handleGrantAdmin(user.id)"
                        style="font-size: 12px; padding: 4px 8px; background: var(--color-primary); color: white; border: none; border-radius: 4px; cursor: pointer;"
                      >
                        Jadikan Admin
                      </button>
                      <button
                        v-if="user.is_superuser"
                        @click="handleRevokeAdmin(user.id)"
                        style="font-size: 12px; padding: 4px 8px; background: var(--color-muted); color: white; border: none; border-radius: 4px; cursor: pointer;"
                      >
                        Cabut Admin
                      </button>
                      <button
                        @click="handleDeleteUser(user.id)"
                        style="font-size: 12px; padding: 4px 8px; background: red; color: white; border: none; border-radius: 4px; cursor: pointer;"
                      >
                        Hapus
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div v-if="userActionError" class="error-message">{{ userActionError }}</div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/**** Uses global tokens and button style: #2e5a35, radius 20px, padding ****/
</style>

