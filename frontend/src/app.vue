<template>
  <div class="app">
    <main class="main" role="main">
      <router-view v-slot="{ Component, route }">
        <div class="view-wrapper" :key="route.path">
          <component :is="Component" @loading="setLoading" v-if="!isLoading" />
          <div v-else class="loading">Loading...</div>
        </div>
      </router-view>
    </main>
  </div>
</template>

<script setup lang="ts">
// import * as Vue from "vue";
import { ref } from "vue";

// Reactive data
const isLoading = ref<boolean>(false);

// Computed properties

// Methods
const setLoading = (status: boolean): void => {
  isLoading.value = status;
};
</script>

<style>
.app,
.main {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font: inherit;
  line-height: 1;
  box-sizing: border-box;
  background-color: #ffffff;
  color: #2c4a3e;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100vh;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  font-size: 1.5rem;
}

.view-wrapper {
  width: 100%;
  max-width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}
</style>
