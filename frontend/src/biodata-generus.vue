<template>
    <FormContainer>
        <div class="form-title">FORMULIR PENDATAAN</div>
        <div class="form-subtitle">GENERUS {{ daerahParam || "SKC" }}</div>

        <div v-if="!showReview && !showSuccess">
            <PersonalInfoForm :formData="formData" />

            <KelompokSelector
                :formData="formData"
                :kelompokOptions="kelompokData.options"
                :flattenedKelompok="flattenedKelompok"
                :dataLoaded="kelompokData.dataLoaded"
                :isLoading="kelompokData.isLoading"
                :loadError="kelompokData.loadError"
                @input-change="handleKelompokInputChange"
            />

            <SekolahKelasSelector
                :formData="formData"
                :sekolahKelasOptions="sekolahKelasOptions"
            />

            <HobiSelector
                :hobiOptions="hobiData.options"
                v-model:selectedHobi="selectedHobi"
            />

            <PhotoUpload v-model="selectedPhoto" />

            <div class="spaci"></div>

            <ParentInfoForm :formData="formData" />

            <button @click="showReviewData">Review Data</button>
        </div>

        <!-- Review section -->
        <ReviewDataSection
            v-if="showReview && !showSuccess"
            :formData="formData"
            :selectedHobi="selectedHobi"
            :selectedPhoto="selectedPhoto"
            :isSubmitting="isSubmitting"
            @submit="submitToAPI"
            @edit="editForm"
        />

        <!-- Success message -->
        <div v-if="showSuccess" class="confirmation-container">
            <div class="confirmation-message">
                Data berhasil dikirim!<br />
                Alhamdulillah Jazaa Kumullohu Khoiro.
            </div>
            <button @click="resetForm">Isi Formulir Baru</button>
        </div>
    </FormContainer>
</template>

<script setup lang="ts">
import * as Vue from "vue";
import type {
    FormData,
    KelompokData,
    HobiData,
    FlattenedKelompok,
    SekolahKelasOption,
    HobiItem,
    EditFormData,
} from "./types/biodata";

import FormContainer from "./components/FormContainer.vue";
import PersonalInfoForm from "./components/PersonalInfoForm.vue";
import ParentInfoForm from "./components/ParentInfoForm.vue";
import HobiSelector from "./components/HobiSelector.vue";
import KelompokSelector from "./components/KelompokSelector.vue";
import SekolahKelasSelector from "./components/SekolahKelasSelector.vue";
import ReviewDataSection from "./components/ReviewDataSection.vue";
import PhotoUpload from "./components/PhotoUpload.vue";

// Reactive data
const formData = Vue.ref<FormData>({
    nama_lengkap: "",
    nama_panggilan: "",
    jenis_kelamin: "",
    kelahiran_tempat: "",
    kelahiran_tanggal: "",
    alamat_tinggal: "",
    sambung_desa: "",
    sambung_kelompok: "",
    sekolah: "",
    kelas: "",
    nama_ayah: "",
    nama_ibu: "",
    status_ayah: "",
    status_ibu: "",
    nomor_hape_ayah: "",
    nomor_hape_ibu: "",
    catatan: "",
    pendataan_tanggal: "",
});

const daerahParam = Vue.ref<string>("");
const selectedPhoto = Vue.ref<File | null>(null);
const showSuccess = Vue.ref<boolean>(false);
const showReview = Vue.ref<boolean>(false);
const isSubmitting = Vue.ref<boolean>(false);
const apiKey = Vue.ref<string>("");
const showApiKeyError = Vue.ref<boolean>(false);

const kelompokData = Vue.ref<KelompokData>({
    options: {},
    isLoading: true,
    loadError: null,
    dataLoaded: false,
});

const hobiData = Vue.ref<HobiData>({
    options: [],
    isLoading: true,
    loadError: null,
    dataLoaded: false,
});

const sekolahKelasOptions = Vue.ref<SekolahKelasOption[]>([]);
const selectedHobi = Vue.ref<HobiItem[]>([]);

// Computed properties
const flattenedKelompok = Vue.computed<FlattenedKelompok[]>(() => {
    const flattened: FlattenedKelompok[] = [];
    for (const [desa, kelompokList] of Object.entries(
        kelompokData.value.options,
    ) as [string, string[]][]) {
        kelompokList.forEach((kelompok: string) => {
            flattened.push({ desa, kelompok });
        });
    }
    return flattened;
});

// Methods from mixin
const getCurrentDate = (): string => {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, "0");
    const day = String(today.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
};

const fetchDataFromApi = async <T,>(
    url: string,
    stateObj: { value: any },
    processDataFn?: (data: any) => T,
    errorMessage?: string,
    retryFn?: () => void,
): Promise<T | null> => {
    console.log(`Fetching data from ${url}...`);
    try {
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error("Network response was not ok");
        }

        const data: any = await response.json();
        const extractedData = data.data || data;
        const processedData = processDataFn
            ? processDataFn(extractedData)
            : extractedData;

        stateObj.value.options = processedData;
        stateObj.value.isLoading = false;
        stateObj.value.dataLoaded = true;
        stateObj.value.loadError = null;

        console.log(`Data loaded successfully from ${url}`);
        return processedData;
    } catch (error) {
        console.error(`Error fetching data from ${url}:`, error);
        stateObj.value.loadError =
            errorMessage || "Gagal memuat data. Silakan muat ulang halaman.";
        stateObj.value.isLoading = false;
        stateObj.value.dataLoaded = false;

        if (retryFn) {
            setTimeout(retryFn, 5000);
        }
        return null;
    }
};

const fetchKelompokData = async (dataParam: string | null): Promise<void> => {
    if (!dataParam) {
        console.error("Parameter data tidak ditemukan");
        kelompokData.value.loadError = "Parameter data tidak ditemukan";
        kelompokData.value.isLoading = false;
        return;
    }

    const encodedParam = encodeURIComponent(dataParam);
    const url = `/api/data/daerah/${encodedParam}/`;

    const processData = (data: any[]) => {
        const formattedData: { [key: string]: string[] } = {};
        let preselectedItem: { desa: string; kelompok: string } | null = null;

          const dataArray = Array.isArray(data) ? data : [];

          dataArray.forEach((item: { ranah: string; detail_ranah: string }) => {
              const list =
                  formattedData[item.ranah] ?? (formattedData[item.ranah] = []);
              list.push(item.detail_ranah);

            if (
                dataParam &&
                (item.ranah.toLowerCase().includes(dataParam.toLowerCase()) ||
                    item.detail_ranah
                        .toLowerCase()
                        .includes(dataParam.toLowerCase()))
            ) {
                preselectedItem = {
                    desa: item.ranah,
                    kelompok: item.detail_ranah,
                };
            }
        });

          if (preselectedItem) {
              const { desa, kelompok } = preselectedItem;
              setTimeout(() => {
                  formData.value.sambung_desa = desa;
                  formData.value.sambung_kelompok = kelompok;
              }, 0);
          }

        return formattedData;
    };

    await fetchDataFromApi(
        url,
        kelompokData,
        processData,
        "Gagal memuat data kelompok. Silakan muat ulang halaman.",
        () => fetchKelompokData(dataParam),
    );
};

const fetchHobiData = async (): Promise<void> => {
    await fetchDataFromApi(
        "/api/data/hobi",
        hobiData,
        undefined,
        "Gagal memuat data hobi. Silakan muat ulang halaman.",
        () => fetchHobiData(),
    );
};

const fetchSekolahKelasData = async (): Promise<void> => {
    try {
        const response = await fetch("/api/data/kelas-sekolah");
        if (!response.ok) {
            throw new Error("Network response was not ok");
        }
        const data: any = await response.json();
        sekolahKelasOptions.value = data.data || data;
    } catch (error) {
        console.error("Error fetching sekolah/kelas data:", error);
    }
};

const validateFormFields = (): boolean => {
    const { sambung_kelompok, sambung_desa } = formData.value;
    if (!sambung_kelompok || !sambung_desa) {
        alert(
            "Pilihan Desa & Kelompok wajib diisi. Silakan ketik dan pilih dari daftar yang muncul.",
        );
        return false;
    }

    const isValidKelompok = flattenedKelompok.value.some(
        (item: FlattenedKelompok) =>
            item.kelompok === sambung_kelompok && item.desa === sambung_desa,
    );

    if (!isValidKelompok) {
        alert(
            "Silahkan pilih kelompok sesuai dengan pilihan yang muncul saat Anda mengetik",
        );
        return false;
    }
    return true;
};

const showReviewData = (): void => {
    if (!validateFormFields()) {
        return;
    }
    showReview.value = true;
};

const editForm = (data?: EditFormData): void => {
    console.log(
        "EditForm called with alamat_tinggal:",
        formData.value.alamat_tinggal,
    );

    const savedAddress =
        data && data.savedAddress
            ? data.savedAddress
            : formData.value.alamat_tinggal;

    showReview.value = false;

    // Use Vue 3 nextTick
    setTimeout(() => {
        if (
            savedAddress &&
            (!formData.value.alamat_tinggal ||
                formData.value.alamat_tinggal !== savedAddress)
        ) {
            console.log("Restoring alamat_tinggal to:", savedAddress);
            formData.value.alamat_tinggal = savedAddress;
        }
        console.log(
            "After toggling, alamat_tinggal is:",
            formData.value.alamat_tinggal,
        );
    }, 0);
};

const submitToAPI = async (): Promise<void> => {
    if (isSubmitting.value) return;

    console.log("=== SUBMISSION DEBUG START ===");
    console.log("API Key present:", !!apiKey.value);

    if (!formData.value.sambung_desa || !formData.value.sambung_kelompok) {
        console.error("Missing required Desa/Kelompok fields");
        alert("Pilihan Desa & Kelompok wajib diisi sebelum mengirim data.");
        return;
    }

    if (!apiKey.value) {
        console.error("API key missing, aborting submission");
        alert("API key diperlukan. Silakan gunakan URL dengan parameter key.");
        showApiKeyError.value = true;
        return;
    }

    isSubmitting.value = true;
    console.log("Starting form submission process");

    try {
        const formDataToSend = new FormData();
        console.log("Created new FormData object");

        formDataToSend.append("sambung_desa", formData.value.sambung_desa);
        formDataToSend.append(
            "sambung_kelompok",
            formData.value.sambung_kelompok,
        );

        if (selectedHobi.value.length > 0) {
            const groupedHobi: { [key: string]: string[] } = {};
            selectedHobi.value.forEach((item: HobiItem) => {
                  const list = groupedHobi[item.kategori] ?? (groupedHobi[item.kategori] = []);
                  list.push(item.hobi);
            });

            const hobiObj: { [key: string]: string } = {};
            for (const [kategori, hobiList] of Object.entries(groupedHobi)) {
                hobiObj[kategori] = hobiList.join(", ");
            }

            const hobiJson = JSON.stringify(hobiObj);
            formDataToSend.append("hobi", hobiJson);
        } else {
            formDataToSend.append("hobi", JSON.stringify({}));
        }

        for (const key in formData.value) {
            if (["hobi", "sambung_desa", "sambung_kelompok"].includes(key)) {
                continue;
            }

            const value = formData.value[key as keyof FormData];
            if (value !== null && value !== undefined && value !== "") {
                formDataToSend.append(key, String(value));
            }
        }

        const apiUrl = "/api/biodata/generus";
        const response = await fetch(apiUrl, {
            method: "POST",
            headers: {
                Authorization: "ApiKey " + apiKey.value,
            },
            body: formDataToSend,
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error("Error response from server:", errorText);
            throw new Error(`Server error: ${response.status} - ${errorText}`);
        }

        const responseData: any = await response.json();
        console.log("Success response:", responseData);

        showReview.value = false;
        showSuccess.value = true;
    } catch (error) {
        console.error("Error in submission process:", error);
        alert("Terjadi kesalahan saat mengirim data. Mohon coba lagi.");
    } finally {
        isSubmitting.value = false;
        console.log("=== SUBMISSION DEBUG END ===");
    }
};

const resetForm = (): void => {
    window.location.reload();
};

const handleKelompokInputChange = (): void => {
    if (!kelompokData.value.dataLoaded && !kelompokData.value.isLoading) {
        console.log("Data not loaded, retrying fetch...");
        const dataParam = new URLSearchParams(window.location.search).get(
            "data",
        );
        fetchKelompokData(dataParam);
    }
};

// Lifecycle
Vue.onMounted(async () => {
    const params = new URLSearchParams(window.location.search);
    apiKey.value = params.get("key") || "";
    const dataParam = params.get("data");
    daerahParam.value = params.get("daerah") || "";

    // Initialize form data
    formData.value = {
        ...formData.value,
        pendataan_tanggal: getCurrentDate(),
    };

    // Fetch initial data
    await Promise.all([
        fetchKelompokData(dataParam),
        fetchHobiData(),
        fetchSekolahKelasData(),
    ]);
});
</script>
