<template>
  <div>
    <div class="form-row">
      <label for="jalan">Jalan :</label>
      <input
        id="jalan"
        type="text"
        ref="jalanInputEl"
        :value="jalan"
        @input="handleJalanInput"
        @keydown="handleJalanKeydown"
        @change="persistJalanChange($event)"
        placeholder="<PERSON><PERSON> Jalan, Gang, dan RT/RW (jika ada)."
        required
      />
    </div>
    <div class="form-row">
      <label for="nomor">No. :</label>
      <input
        id="nomor"
        type="text"
        ref="nomorInputEl"
        :value="nomor"
        @input="handleNomorInput"
        @keydown="handleNomorKeydown"
        @change="persistNomorChange($event)"
        placeholder="Nomor Rumah. Tulis 0 jika tidak ada."
        required
      />
    </div>
    <!-- Provinsi input field using SuggestionsMixin -->
    <div class="form-row">
      <label for="provinsi">Provinsi :</label>
      <div class="suggestions-container">
        <input
          id="provinsi"
          ref="inputEl"
          type="text"
          v-model="inputValue"
          @input="handleInput"
          @focus="handleFocus"
          @blur="handleBlur"
          @keyup="handleKeyup"
          @compositionstart="handleCompositionStart"
          @compositionend="handleCompositionEnd"
          placeholder="Ketik untuk mencari provinsi"
          required
        />
        <div v-if="isLoadingProvinsi" class="loading-indicator">Loading...</div>
        <div
          class="suggestions"
          v-if="showSuggestions && filteredProvinsiList.length"
        >
          <div
            v-for="item in filteredProvinsiList"
            :key="item.code"
            class="suggestion-item"
            @click="selectProvinsi(item)"
          >
            {{ item.name }}
          </div>
        </div>
      </div>
    </div>
    <div class="form-row" v-if="kabupatenList.length > 0">
      <label for="kabupaten">Kota / Kab. :</label>
      <div class="suggestions-container">
        <input
          id="kabupaten"
          ref="kabupatenInputEl"
          type="text"
          v-model="kabupatenInput"
          @input="handleKabupatenInput($event)"
          @focus="handleKabupatenFocus"
          @blur="handleKabupatenBlur"
          @keyup="handleKabupatenKeyup"
          @compositionstart="handleCompositionStart"
          @compositionend="handleKabupatenCompositionEnd"
          placeholder="Ketik untuk mencari kabupaten"
          required
        />
        <div v-if="isLoadingKabupaten" class="loading-indicator">
          Loading...
        </div>
        <div
          class="suggestions"
          v-if="showKabupatenSuggestions && filteredKabupatenList.length"
        >
          <div
            v-for="item in filteredKabupatenList"
            :key="item.code"
            class="suggestion-item"
            @click="selectKabupaten(item)"
          >
            {{ item.name }}
          </div>
        </div>
      </div>
    </div>
    <div class="form-row" v-if="kecamatanList.length > 0">
      <label for="kecamatan">Kecamatan :</label>
      <div class="suggestions-container">
        <input
          id="kecamatan"
          ref="kecamatanInputEl"
          type="text"
          v-model="kecamatanInput"
          @input="handleKecamatanInput($event)"
          @focus="handleKecamatanFocus"
          @blur="handleKecamatanBlur"
          @keyup="handleKecamatanKeyup"
          @compositionstart="handleCompositionStart"
          @compositionend="handleKecamatanCompositionEnd"
          placeholder="Ketik untuk mencari kecamatan"
          required
        />
        <div v-if="isLoadingKecamatan" class="loading-indicator">
          Loading...
        </div>
        <div
          class="suggestions"
          v-if="showKecamatanSuggestions && filteredKecamatanList.length"
        >
          <div
            v-for="item in filteredKecamatanList"
            :key="item.code"
            class="suggestion-item"
            @click="selectKecamatan(item)"
          >
            {{ item.name }}
          </div>
        </div>
      </div>
    </div>
    <div class="form-row" v-if="kelurahanList.length > 0">
      <label for="kelurahan">Desa / Kel. :</label>
      <div class="suggestions-container">
        <input
          id="kelurahan"
          ref="kelurahanInputEl"
          type="text"
          v-model="kelurahanInput"
          @input="handleKelurahanInput($event)"
          @focus="handleKelurahanFocus"
          @blur="handleKelurahanBlur"
          @keyup="handleKelurahanKeyup"
          @compositionstart="handleCompositionStart"
          @compositionend="handleKelurahanCompositionEnd"
          placeholder="Ketik untuk mencari kelurahan/desa"
          required
        />
        <div v-if="isLoadingKelurahan" class="loading-indicator">
          Loading...
        </div>
        <div
          class="suggestions"
          v-if="showKelurahanSuggestions && filteredKelurahanList.length"
        >
          <div
            v-for="item in filteredKelurahanList"
            :key="item.code"
            class="suggestion-item"
            @click="selectKelurahan(item)"
          >
            {{ item.name }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import * as Vue from "vue";

type Timer = ReturnType<typeof setTimeout> | null;

interface RegionItem {
  code: string;
  name: string;
  // Allow additional fields from API responses without strict typing
  [key: string]: unknown;
}

interface CacheEntry {
  data: RegionItem[];
  timestamp: number;
}

type DataModel = {
  jalan: string;
  nomor: string;
  provinsiList: RegionItem[];
  kabupatenList: RegionItem[];
  kecamatanList: RegionItem[];
  kelurahanList: RegionItem[];

  selectedProvinsi: RegionItem | null;
  selectedKabupaten: RegionItem | null;
  selectedKecamatan: RegionItem | null;
  selectedKelurahan: RegionItem | null;

  kabupatenInput: string;
  kecamatanInput: string;
  kelurahanInput: string;

  showKabupatenSuggestions: boolean;
  showKecamatanSuggestions: boolean;
  showKelurahanSuggestions: boolean;

  isLoadingProvinsi: boolean;
  isLoadingKabupaten: boolean;
  isLoadingKecamatan: boolean;
  isLoadingKelurahan: boolean;

  isUpdating: boolean;

  updateTimer: Timer;

  wilayahCache: Map<string, CacheEntry>;

  inputDebounceTimers: {
    kabupaten: Timer;
    kecamatan: Timer;
    kelurahan: Timer;
  };

  provinsiSelected: boolean;
  kabupatenSelected: boolean;
  kecamatanSelected: boolean;
  kelurahanSelected: boolean;

  originalAddressState: {
    jalan: string;
    nomor: string;
    provinsi: string | null;
    kabupaten: string | null;
    kecamatan: string | null;
    kelurahan: string | null;
  } | null;

  lastEmittedAddress: string;

  isUserEditing: boolean;
  userEditingTimer: Timer;

  persistedJalan: string;
  persistedNomor: string;

  inputValue: string;
  showSuggestions: boolean;
  isComposing: boolean;
};

type ThisCtx = DataModel &
  Vue.ComponentPublicInstance & {
    value: string;
    modelValue?: string;
    [key: string]: any;
  };

export default Vue.defineComponent({
  name: "AlamatTinggalForm",
  props: {
    value: {
      type: String,
      required: true,
    },
    modelValue: {
      type: String,
      default: undefined,
    },
  },
  emits: [
    "input",
    "update:modelValue",
    "input-change",
    "kabupaten-change",
    "kecamatan-change",
    "kelurahan-change",
  ],
  data(): DataModel {
    return {
      jalan: "" as string,
      nomor: "" as string,
      provinsiList: [] as RegionItem[],
      kabupatenList: [] as RegionItem[],
      kecamatanList: [] as RegionItem[],
      kelurahanList: [] as RegionItem[],

      selectedProvinsi: null as RegionItem | null,
      selectedKabupaten: null as RegionItem | null,
      selectedKecamatan: null as RegionItem | null,
      selectedKelurahan: null as RegionItem | null,

      // For inputs not handled by mixin
      kabupatenInput: "" as string,
      kecamatanInput: "" as string,
      kelurahanInput: "" as string,

      showKabupatenSuggestions: false as boolean,
      showKecamatanSuggestions: false as boolean,
      showKelurahanSuggestions: false as boolean,

      isLoadingProvinsi: false as boolean,
      isLoadingKabupaten: false as boolean,
      isLoadingKecamatan: false as boolean,
      isLoadingKelurahan: false as boolean,

      // Add an update lock flag to prevent circular updates
      isUpdating: false as boolean,

      // Add debounce timer
      updateTimer: null as Timer,

      // Cache for API responses to improve performance
      wilayahCache: new Map<string, CacheEntry>(),

      // Debounce timers for input handling
      inputDebounceTimers: {
        kabupaten: null as Timer,
        kecamatan: null as Timer,
        kelurahan: null as Timer,
      },

      // Add flags to track if an item has been selected
      provinsiSelected: false as boolean,
      kabupatenSelected: false as boolean,
      kecamatanSelected: false as boolean,
      kelurahanSelected: false as boolean,

      // Store original address state to detect changes
      originalAddressState: null as {
        jalan: string;
        nomor: string;
        provinsi: string | null;
        kabupaten: string | null;
        kecamatan: string | null;
        kelurahan: string | null;
      } | null,

      // Add a property to store the last emitted address for comparison
      lastEmittedAddress: "" as string,

      // Add flags to track user editing state
      isUserEditing: false as boolean,
      userEditingTimer: null as Timer,

      // Add permanent persistence for address components
      persistedJalan: "" as string,
      persistedNomor: "" as string,

      // Suggestions state (formerly from mixin)
      inputValue: "" as string,
      showSuggestions: false as boolean,
      isComposing: false as boolean,
    };
  },
  computed: {
    filteredProvinsiList(this: ThisCtx): RegionItem[] {
      const iv: string = this.inputValue || "";
      const searchTerm = iv.toLowerCase();
      if (!searchTerm) return [];
      return this.provinsiList.filter((item) =>
        item.name.toLowerCase().includes(searchTerm),
      );
    },
    filteredKabupatenList(this: ThisCtx): RegionItem[] {
      const searchTerm = (this.kabupatenInput || "").toLowerCase();
      if (!searchTerm) return [];
      return this.kabupatenList.filter((item) =>
        item.name.toLowerCase().includes(searchTerm),
      );
    },
    filteredKecamatanList(this: ThisCtx): RegionItem[] {
      const searchTerm = (this.kecamatanInput || "").toLowerCase();
      if (!searchTerm) return [];
      return this.kecamatanList.filter((item) =>
        item.name.toLowerCase().includes(searchTerm),
      );
    },
    filteredKelurahanList(this: ThisCtx): RegionItem[] {
      const searchTerm = (this.kelurahanInput || "").toLowerCase();
      if (!searchTerm) return [];
      return this.kelurahanList.filter((item) =>
        item.name.toLowerCase().includes(searchTerm),
      );
    },
  },
  watch: {
    jalan: "updateAlamat",
    nomor: "updateAlamat",
    selectedProvinsi: "updateAlamat",
    selectedKabupaten: "updateAlamat",
    selectedKecamatan: "updateAlamat",
    selectedKelurahan: "updateAlamat",
    value: {
      immediate: true,
      handler(this: ThisCtx, newVal: unknown) {
        // safer type check
        if (
          !(this as any).isUpdating &&
          typeof newVal === "string" &&
          newVal.trim() !== ""
        ) {
          this.parseExistingAddress(newVal);
        }
      },
    },
    modelValue: {
      immediate: true,
      handler(this: ThisCtx, newVal: unknown) {
        // safer type check
        if (
          !(this as any).isUpdating &&
          typeof newVal === "string" &&
          newVal.trim() !== ""
        ) {
          this.parseExistingAddress(newVal);
        }
      },
    },
    "$parent.isActive": function (this: ThisCtx, isActive: boolean) {
      if (!isActive) {
        this.forceAddressUpdate();
      }
    },
  },
  methods: {
    // Add keydown handlers to prevent cursor jumps on space
    handleJalanKeydown(this: ThisCtx, event: KeyboardEvent) {
      event.stopPropagation();
      if (event.key === " " || (event as any).keyCode === 32) {
        event.stopPropagation();
      }
    },

    handleNomorKeydown(this: ThisCtx, event: KeyboardEvent) {
      event.stopPropagation();
      if (event.key === " " || (event as any).keyCode === 32) {
        event.stopPropagation();
      }
    },

    // Improve the input handlers to better maintain focus
    handleJalanInput(this: ThisCtx, event: Event) {
      event.stopPropagation();
      const target = event.target as HTMLInputElement | null;
      if (!target) return;

      const cursorPosition = target.selectionStart ?? target.value.length;
      const scrollPosition = target.scrollTop;

      const activeElement = document.activeElement as HTMLElement | null;

      this.jalan = target.value;
      this.persistedJalan = target.value;

      try {
        localStorage.setItem("alamat_jalan_temp", target.value);
      } catch {
        // no-op
      }

      if (this.originalAddressState) {
        this.originalAddressState.jalan = target.value;
      }

      this.isUserEditing = true;
      if (this.userEditingTimer) clearTimeout(this.userEditingTimer);

      this.$nextTick(() => {
        const jalanEl = this.$refs.jalanInputEl as HTMLInputElement | undefined;
        if (activeElement && jalanEl && activeElement === jalanEl) {
          jalanEl.focus();
          try {
            jalanEl.setSelectionRange(cursorPosition, cursorPosition);
          } catch {
            // some browsers might throw on invalid ranges
          }
          jalanEl.scrollTop = scrollPosition;
        }

        this.userEditingTimer = setTimeout(() => {
          this.isUserEditing = false;
        }, 1000);
      });
    },

    handleNomorInput(this: ThisCtx, event: Event) {
      event.stopPropagation();
      const target = event.target as HTMLInputElement | null;
      if (!target) return;

      const cursorPosition = target.selectionStart ?? target.value.length;
      const scrollPosition = target.scrollTop;
      const activeElement = document.activeElement as HTMLElement | null;

      const newNomor = target.value;
      this.nomor = newNomor;
      this.persistedNomor = newNomor;

      const isRealUserInput =
        (event as unknown as { isTrusted?: boolean }).isTrusted !== false;

      if (isRealUserInput) {
        try {
          localStorage.setItem("alamat_nomor", newNomor);
          localStorage.setItem("alamat_nomor_priority", "true");
          sessionStorage.setItem("alamat_nomor_current", newNomor);
        } catch {
          // no-op
        }
      }

      try {
        localStorage.setItem("alamat_nomor_temp", target.value);
      } catch {
        // no-op
      }

      if (this.originalAddressState) {
        this.originalAddressState.nomor = target.value;
      }

      this.isUserEditing = true;
      if (this.userEditingTimer) clearTimeout(this.userEditingTimer);

      this.$nextTick(() => {
        const nomorEl = this.$refs.nomorInputEl as HTMLInputElement | undefined;
        if (activeElement && nomorEl && activeElement === nomorEl) {
          nomorEl.focus();
          try {
            nomorEl.setSelectionRange(cursorPosition, cursorPosition);
          } catch {
            // ignore
          }
          nomorEl.scrollTop = scrollPosition;
        }

        this.userEditingTimer = setTimeout(() => {
          this.isUserEditing = false;
        }, 1000);
      });
    },

    persistJalanChange(this: ThisCtx, event: Event) {
      const target = event.target as HTMLInputElement | null;
      const value = target?.value ?? this.jalan;
      this.jalan = value;
      this.persistedJalan = value;

      try {
        localStorage.setItem("alamat_jalan", value);
      } catch {
        // no-op
      }

      this.isUserEditing = false;
      this.forceAddressUpdate();
    },

    persistNomorChange(this: ThisCtx, event: Event) {
      const target = event.target as HTMLInputElement | null;
      const newNomor = target?.value ?? this.nomor;

      this.nomor = newNomor;
      this.persistedNomor = newNomor;

      try {
        localStorage.setItem("alamat_nomor", newNomor);
        localStorage.setItem("alamat_nomor_priority", "true");
      } catch {
        // no-op
      }

      this.isUserEditing = false;
      this.isUpdating = false;

      this.forceAddressUpdateWithNumber(newNomor);
    },

    forceAddressUpdateWithNumber(this: ThisCtx, forcedNumber: string) {
      if (this.updateTimer) clearTimeout(this.updateTimer);

      const currentJalan = this.persistedJalan || this.jalan || "";

      const alamatParts: string[] = [];

      if (currentJalan.trim() !== "") {
        alamatParts.push(`Jl. ${currentJalan.trim()},`);
      }

      if (forcedNumber && forcedNumber.trim() !== "") {
        alamatParts.push(`No. ${forcedNumber.trim()},`);
      }

      if (this.selectedKelurahan && this.kelurahanSelected) {
        const kelurahanName = (this.selectedKelurahan.name || "")
          .toString()
          .trim();
        if (kelurahanName) alamatParts.push(`Desa/Kel. ${kelurahanName},`);
      }

      if (this.selectedKecamatan && this.kecamatanSelected) {
        alamatParts.push(`Kec. ${this.selectedKecamatan.name},`);
      }

      if (this.selectedKabupaten?.name) {
        alamatParts.push(`${this.selectedKabupaten.name},`);
      }

      if (this.selectedProvinsi?.name) {
        alamatParts.push(`${this.selectedProvinsi.name}`);
      }

      if (alamatParts.length > 0) {
        const alamat = alamatParts.join(" ");
        this.lastEmittedAddress = alamat;
        this.$emit("input", alamat);
        this.$emit("update:modelValue", alamat);
      }
    },

    // Improve handleInput for provinsi to better isolate changes
    handleInput(this: ThisCtx, event?: Event) {
      if (event) event.stopPropagation();

      if (
        this.selectedProvinsi &&
        this.inputValue !== this.selectedProvinsi.name
      ) {
        this.provinsiSelected = false;
        this.selectedProvinsi = null;
      }

      if (!this.provinsiSelected) {
        this.showSuggestions = true;
      }

      this.$emit("input-change");
    },

    // Modify handleFocus to respect selection state
    handleFocus(this: ThisCtx) {
      if (!this.provinsiSelected && this.inputValue) {
        this.showSuggestions = true;
      }
    },

    // Modify handleBlur to ensure suggestions are hidden
    handleBlur(this: ThisCtx) {
      setTimeout(() => {
        this.showSuggestions = false;
        if (!this.selectedProvinsi && !this.provinsiSelected) {
          this.inputValue = "";
        }
      }, 150);
    },

    // Province input helpers (formerly from mixin)
    handleKeyup(this: ThisCtx, _event: KeyboardEvent) {
      const inputEl = this.$refs.inputEl as HTMLInputElement | undefined;
      if (inputEl && this.inputValue !== inputEl.value) {
        this.inputValue = inputEl.value;
        this.handleInput();
      }
    },

    handleCompositionStart(this: ThisCtx) {
      this.isComposing = true;
    },

    handleCompositionEnd(this: ThisCtx, event: CompositionEvent) {
      this.isComposing = false;
      const target = event.target as HTMLInputElement | null;
      this.inputValue = target?.value ?? "";
      this.handleInput();
    },

    // Cache management methods
    getCacheKey(this: ThisCtx, type: string, code: string | null = null) {
      return code ? `${type}_${code}` : type;
    },

    getCachedData(
      this: ThisCtx,
      type: string,
      code: string | null = null,
    ): RegionItem[] | null {
      const key = this.getCacheKey(type, code);
      const cached = this.wilayahCache.get(key);

      if (cached) {
        const now = Date.now();
        if (now - cached.timestamp < 5 * 60 * 1000) {
          return cached.data;
        } else {
          this.wilayahCache.delete(key);
        }
      }
      return null;
    },

    setCachedData(
      this: ThisCtx,
      type: string,
      data: RegionItem[],
      code: string | null = null,
    ) {
      const key = this.getCacheKey(type, code);
      this.wilayahCache.set(key, {
        data,
        timestamp: Date.now(),
      });

      if (this.wilayahCache.size > 100) {
        const entries = Array.from(this.wilayahCache.entries());
        entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
        for (const [key] of entries.slice(0, 20)) {
          this.wilayahCache.delete(key);
        }
      }
    },

    // Prefetch popular regions for better performance
    async prefetchPopularRegions(this: ThisCtx) {
      const popularProvinceCodes = ["31", "32", "33", "35"];

      for (const code of popularProvinceCodes) {
        if (!this.getCachedData("regencies", code)) {
          try {
            const response = await fetch(`/api/wilayah/regencies/${code}`);
            if (response.ok) {
              const data = (await response.json()) as {
                data?: RegionItem[];
              };
              if (Array.isArray(data?.data)) {
                this.setCachedData("regencies", data.data, code);
              }
            }
          } catch {
            // prefetch is best-effort
          }
        }
      }
    },

    // Clean up method
    cleanupTimers(this: ThisCtx) {
      if (this.updateTimer) clearTimeout(this.updateTimer);
      Object.values(this.inputDebounceTimers).forEach((timer) => {
        if (timer) clearTimeout(timer);
      });
      if (this.userEditingTimer) clearTimeout(this.userEditingTimer);
    },

    async fetchProvinsi(this: ThisCtx, retryCount = 0): Promise<void> {
      const cachedData = this.getCachedData("provinces");
      if (cachedData) {
        this.provinsiList = cachedData;
        return;
      }

      try {
        this.isLoadingProvinsi = true;

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000);

        const response = await fetch("/api/wilayah/provinces", {
          signal: controller.signal,
          headers: {
            "Cache-Control": "no-cache",
            Pragma: "no-cache",
          },
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          if (response.status === 502 && retryCount < 3) {
            await new Promise((resolve) =>
              setTimeout(resolve, 1000 * (retryCount + 1)),
            );
            return this.fetchProvinsi(retryCount + 1);
          }

          throw new Error(
            `Failed to fetch provinces: ${response.status} ${response.statusText}`,
          );
        }

        const data = (await response.json()) as { data?: RegionItem[] };

        if (!data.data || !Array.isArray(data.data)) {
          throw new Error("Invalid data format received");
        }

        this.provinsiList = data.data;
        this.setCachedData("provinces", data.data);
      } catch (error: any) {
        if (
          ((error?.name as string) === "AbortError" ||
            String(error?.message || "").includes("fetch")) &&
          retryCount < 2
        ) {
          await new Promise((resolve) => setTimeout(resolve, 2000));
          return this.fetchProvinsi(retryCount + 1);
        }

        this.provinsiList = [];
      } finally {
        this.isLoadingProvinsi = false;
      }
    },

    // Improve province selection to prevent UI jumps
    selectProvinsi(this: ThisCtx, item: RegionItem) {
      if (this.selectedProvinsi && this.selectedProvinsi.code === item.code) {
        this.showSuggestions = false;
        this.provinsiSelected = true;
        return;
      }

      this.isUpdating = true;

      this.selectedProvinsi = item;
      this.inputValue = item.name;
      this.showSuggestions = false;
      this.provinsiSelected = true;

      const jalanToKeep = this.jalan;
      const nomorToKeep = this.nomor;

      this.selectedKabupaten = null;
      this.kabupatenInput = "";
      this.selectedKecamatan = null;
      this.kecamatanInput = "";
      this.selectedKelurahan = null;
      this.kelurahanInput = "";
      this.kabupatenSelected = false;
      this.kecamatanSelected = false;
      this.kelurahanSelected = false;

      this.jalan = jalanToKeep;
      this.nomor = nomorToKeep;

      void this.fetchKabupaten().then(() => {
        setTimeout(() => {
          this.isUpdating = false;
          this.updateAlamat();
        }, 300);
      });
    },

    // Debounced input handlers for better performance
    debouncedKabupatenInput(this: ThisCtx, inputValue: string) {
      const t = this.inputDebounceTimers.kabupaten;
      if (t) clearTimeout(t);
      this.inputDebounceTimers.kabupaten = setTimeout(() => {
        this.processKabupatenInput(inputValue);
      }, 150);
    },

    debouncedKecamatanInput(this: ThisCtx, inputValue: string) {
      const t = this.inputDebounceTimers.kecamatan;
      if (t) clearTimeout(t);
      this.inputDebounceTimers.kecamatan = setTimeout(() => {
        this.processKecamatanInput(inputValue);
      }, 150);
    },

    debouncedKelurahanInput(this: ThisCtx, inputValue: string) {
      const t = this.inputDebounceTimers.kelurahan;
      if (t) clearTimeout(t);
      this.inputDebounceTimers.kelurahan = setTimeout(() => {
        this.processKelurahanInput(inputValue);
      }, 150);
    },

    // Processing methods for debounced inputs
    processKabupatenInput(this: ThisCtx, inputValue: string) {
      if (this.isUpdating) return;

      if (
        this.selectedKabupaten &&
        inputValue !== this.selectedKabupaten.name
      ) {
        this.kabupatenSelected = false;
        this.selectedKabupaten = null;
        this.selectedKecamatan = null;
        this.kecamatanInput = "";
        this.selectedKelurahan = null;
        this.kelurahanInput = "";
        this.kecamatanSelected = false;
        this.kelurahanSelected = false;
        this.kecamatanList = [];
        this.kelurahanList = [];
      }

      this.showKabupatenSuggestions = inputValue.length > 0;
    },

    processKecamatanInput(this: ThisCtx, inputValue: string) {
      if (
        this.selectedKecamatan &&
        inputValue !== this.selectedKecamatan.name
      ) {
        this.kecamatanSelected = false;
        this.selectedKecamatan = null;
        this.selectedKelurahan = null;
        this.kelurahanInput = "";
        this.kelurahanSelected = false;
        this.kelurahanList = [];
      }

      this.showKecamatanSuggestions = inputValue.length > 0;
    },

    processKelurahanInput(this: ThisCtx, inputValue: string) {
      if (
        this.selectedKelurahan &&
        inputValue !== this.selectedKelurahan.name
      ) {
        this.kelurahanSelected = false;
        this.selectedKelurahan = null;
      }

      this.showKelurahanSuggestions = inputValue.length > 0;
    },

    // Kabupaten handlers
    handleKabupatenInput(this: ThisCtx, event?: Event) {
      if (event) event.stopPropagation();

      if (this.isUpdating) return;

      this.debouncedKabupatenInput(this.kabupatenInput);

      if (!this.isComposing) {
        this.$emit("kabupaten-change");
      }
    },

    handleKabupatenFocus(this: ThisCtx) {
      if (!this.kabupatenSelected && this.kabupatenInput) {
        this.showKabupatenSuggestions = true;
      }
    },

    handleKabupatenBlur(this: ThisCtx) {
      setTimeout(() => {
        this.showKabupatenSuggestions = false;
        if (!this.selectedKabupaten && !this.kabupatenSelected) {
          this.kabupatenInput = "";
        }
      }, 150);
    },

    handleKabupatenKeyup(this: ThisCtx, event: KeyboardEvent) {
      const inputEl = this.$refs.kabupatenInputEl as
        | HTMLInputElement
        | undefined;
      if (inputEl && this.kabupatenInput !== inputEl.value) {
        this.kabupatenInput = inputEl.value;
        this.handleKabupatenInput();
      }

      if (event.key === "Enter" && this.filteredKabupatenList.length > 0) {
        event.preventDefault();
        this.selectKabupaten(this.filteredKabupatenList[0]);
      }
    },

    handleKabupatenCompositionEnd(this: ThisCtx, event: CompositionEvent) {
      this.isComposing = false;
      const target = event.target as HTMLInputElement | null;
      this.kabupatenInput = target?.value ?? "";
      this.handleKabupatenInput();
    },

    selectKabupaten(this: ThisCtx, item: RegionItem) {
      if (this.selectedKabupaten && this.selectedKabupaten.code === item.code) {
        this.showKabupatenSuggestions = false;
        this.kabupatenSelected = true;
        return;
      }

      this.isUpdating = true;

      const jalanToKeep = this.jalan;
      const nomorToKeep = this.nomor;

      this.selectedKabupaten = item;
      this.kabupatenInput = item.name;
      this.showKabupatenSuggestions = false;
      this.kabupatenSelected = true;

      this.selectedKecamatan = null;
      this.kecamatanInput = "";
      this.selectedKelurahan = null;
      this.kelurahanInput = "";
      this.kecamatanSelected = false;
      this.kelurahanSelected = false;

      this.kecamatanList = [];
      this.kelurahanList = [];

      this.jalan = jalanToKeep;
      this.nomor = nomorToKeep;

      void this.fetchKecamatan().then(() => {
        setTimeout(() => {
          this.isUpdating = false;
          this.updateAlamat();
        }, 300);
      });
    },

    // Kecamatan handlers
    handleKecamatanInput(this: ThisCtx, event: Event) {
      event.stopPropagation();
      this.debouncedKecamatanInput(this.kecamatanInput);

      if (!this.isComposing) {
        this.$emit("kecamatan-change");
      }
    },

    handleKecamatanFocus(this: ThisCtx) {
      if (!this.kecamatanSelected && this.kecamatanInput) {
        this.showKecamatanSuggestions = true;
      }
    },

    handleKecamatanBlur(this: ThisCtx) {
      setTimeout(() => {
        this.showKecamatanSuggestions = false;
        if (!this.selectedKecamatan && !this.kecamatanSelected) {
          this.kecamatanInput = "";
        }
      }, 150);
    },

    handleKecamatanKeyup(this: ThisCtx, event: KeyboardEvent) {
      const inputEl = this.$refs.kecamatanInputEl as
        | HTMLInputElement
        | undefined;
      if (inputEl && this.kecamatanInput !== inputEl.value) {
        this.kecamatanInput = inputEl.value;
        this.handleKecamatanInput(new Event("input"));
      }

      if (event.key === "Enter" && this.filteredKecamatanList.length > 0) {
        event.preventDefault();
        this.selectKecamatan(this.filteredKecamatanList[0]);
      }
    },

    handleKecamatanCompositionEnd(this: ThisCtx, event: CompositionEvent) {
      this.isComposing = false;
      const target = event.target as HTMLInputElement | null;
      this.kecamatanInput = target?.value ?? "";
      this.handleKecamatanInput(new Event("input"));
    },

    selectKecamatan(this: ThisCtx, item: RegionItem) {
      if (this.selectedKecamatan && this.selectedKecamatan.code === item.code) {
        this.showKecamatanSuggestions = false;
        this.kecamatanSelected = true;
        return;
      }

      this.isUpdating = true;

      this.selectedKecamatan = item;
      this.kecamatanInput = item.name;
      this.showKecamatanSuggestions = false;
      this.kecamatanSelected = true;

      this.selectedKelurahan = null;
      this.kelurahanInput = "";
      this.kelurahanSelected = false;
      this.kelurahanList = [];

      void this.fetchKelurahan().then(() => {
        setTimeout(() => {
          this.isUpdating = false;
          this.updateAlamat();
        }, 300);
      });
    },

    // Kelurahan handlers
    handleKelurahanInput(this: ThisCtx, event: Event) {
      event.stopPropagation();
      this.debouncedKelurahanInput(this.kelurahanInput);

      if (!this.isComposing) {
        this.$emit("kelurahan-change");
      }
    },

    handleKelurahanFocus(this: ThisCtx) {
      if (!this.kelurahanSelected && this.kelurahanInput) {
        this.showKelurahanSuggestions = true;
      }
    },

    handleKelurahanBlur(this: ThisCtx) {
      setTimeout(() => {
        this.showKelurahanSuggestions = false;
        if (!this.selectedKelurahan && !this.kelurahanSelected) {
          this.kelurahanInput = "";
        }
      }, 150);
    },

    handleKelurahanKeyup(this: ThisCtx, event: KeyboardEvent) {
      const inputEl = this.$refs.kelurahanInputEl as
        | HTMLInputElement
        | undefined;
      if (inputEl && this.kelurahanInput !== inputEl.value) {
        this.kelurahanInput = inputEl.value;
        this.handleKelurahanInput(new Event("input"));
      }

      if (event.key === "Enter" && this.filteredKelurahanList.length > 0) {
        event.preventDefault();
        this.selectKelurahan(this.filteredKelurahanList[0]);
      }
    },

    handleKelurahanCompositionEnd(this: ThisCtx, event: CompositionEvent) {
      this.isComposing = false;
      const target = event.target as HTMLInputElement | null;
      this.kelurahanInput = target?.value ?? "";
      this.handleKelurahanInput(new Event("input"));
    },

    selectKelurahan(this: ThisCtx, item: RegionItem) {
      if (this.selectedKelurahan && this.selectedKelurahan.code === item.code) {
        this.showKelurahanSuggestions = false;
        this.kelurahanSelected = true;
        return;
      }

      this.isUpdating = true;

      this.selectedKelurahan = {
        ...item,
        name: (item.name || "").toString().trim(),
      };
      this.kelurahanInput = (item.name || "").toString().trim();
      this.showKelurahanSuggestions = false;
      this.kelurahanSelected = true;

      setTimeout(() => {
        this.isUpdating = false;
        this.updateAlamat();
      }, 300);
    },

    // Data fetch methods
    async fetchKabupaten(
      this: ThisCtx,
      retryCount = 0,
    ): Promise<RegionItem[] | void> {
      if (!this.selectedProvinsi) {
        return;
      }

      const cachedData = this.getCachedData(
        "regencies",
        this.selectedProvinsi.code,
      );
      if (cachedData) {
        this.kabupatenList = cachedData;
        this.$nextTick(() => {
          const el = this.$refs.kabupatenInputEl as
            | HTMLInputElement
            | undefined;
          if (el && !this.kabupatenSelected) {
            el.focus();
          }
        });
        return cachedData;
      }

      try {
        this.isLoadingKabupaten = true;
        this.kabupatenList = [];

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000);

        const response = await fetch(
          `/api/wilayah/regencies/${this.selectedProvinsi.code}`,
          {
            signal: controller.signal,
            headers: {
              "Cache-Control": "no-cache",
              Pragma: "no-cache",
            },
          },
        );

        clearTimeout(timeoutId);

        if (!response.ok) {
          if (response.status === 502 && retryCount < 3) {
            await new Promise((resolve) =>
              setTimeout(resolve, 1000 * (retryCount + 1)),
            );
            return this.fetchKabupaten(retryCount + 1);
          }

          throw new Error(
            `Failed to fetch kabupaten: ${response.status} ${response.statusText}`,
          );
        }

        const data = (await response.json()) as { data?: RegionItem[] };

        if (!data.data || !Array.isArray(data.data)) {
          throw new Error("Invalid data format received");
        }

        this.kabupatenList = data.data;
        this.setCachedData("regencies", data.data, this.selectedProvinsi.code);

        return data.data;
      } catch (error: any) {
        if (
          ((error?.name as string) === "AbortError" ||
            String(error?.message || "").includes("fetch")) &&
          retryCount < 2
        ) {
          await new Promise((resolve) => setTimeout(resolve, 2000));
          return this.fetchKabupaten(retryCount + 1);
        }

        this.kabupatenList = [];

        return [];
      } finally {
        this.isLoadingKabupaten = false;

        this.$nextTick(() => {
          const el = this.$refs.kabupatenInputEl as
            | HTMLInputElement
            | undefined;
          if (el && !this.kabupatenSelected) {
            el.focus();
          }
        });
      }
    },

    async fetchKecamatan(
      this: ThisCtx,
      retryCount = 0,
    ): Promise<RegionItem[] | void> {
      if (!this.selectedKabupaten) {
        return [];
      }

      const cachedData = this.getCachedData(
        "districts",
        this.selectedKabupaten.code,
      );
      if (cachedData) {
        this.kecamatanList = cachedData;
        this.$nextTick(() => {
          const el = this.$refs.kecamatanInputEl as
            | HTMLInputElement
            | undefined;
          if (el && !this.kecamatanSelected) {
            el.focus();
          }
        });
        return cachedData;
      }

      try {
        this.isLoadingKecamatan = true;
        this.kecamatanList = [];

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000);

        const response = await fetch(
          `/api/wilayah/districts/${this.selectedKabupaten.code}`,
          {
            signal: controller.signal,
            headers: {
              "Cache-Control": "no-cache",
              Pragma: "no-cache",
            },
          },
        );

        clearTimeout(timeoutId);

        if (!response.ok) {
          if (response.status === 502 && retryCount < 3) {
            await new Promise((resolve) =>
              setTimeout(resolve, 1000 * (retryCount + 1)),
            );
            return this.fetchKecamatan(retryCount + 1);
          }

          throw new Error(
            `Failed to fetch kecamatan: ${response.status} ${response.statusText}`,
          );
        }

        const data = (await response.json()) as { data?: RegionItem[] };

        if (!data.data || !Array.isArray(data.data)) {
          throw new Error("Invalid data format received");
        }

        this.kecamatanList = data.data;
        this.setCachedData("districts", data.data, this.selectedKabupaten.code);

        return data.data;
      } catch (error: any) {
        if (
          ((error?.name as string) === "AbortError" ||
            String(error?.message || "").includes("fetch")) &&
          retryCount < 2
        ) {
          await new Promise((resolve) => setTimeout(resolve, 2000));
          return this.fetchKecamatan(retryCount + 1);
        }

        this.kecamatanList = [];
        return [];
      } finally {
        this.isLoadingKecamatan = false;

        this.$nextTick(() => {
          const el = this.$refs.kecamatanInputEl as
            | HTMLInputElement
            | undefined;
          if (el && !this.kecamatanSelected) {
            el.focus();
          }
        });
      }
    },

    async fetchKelurahan(
      this: ThisCtx,
      retryCount = 0,
    ): Promise<RegionItem[] | void> {
      if (!this.selectedKecamatan) {
        return;
      }

      const cachedData = this.getCachedData(
        "villages",
        this.selectedKecamatan.code,
      );
      if (cachedData) {
        this.kelurahanList = cachedData;
        this.$nextTick(() => {
          const el = this.$refs.kelurahanInputEl as
            | HTMLInputElement
            | undefined;
          if (el) el.focus();
        });
        return cachedData;
      }

      try {
        this.isLoadingKelurahan = true;
        this.kelurahanList = [];

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000);

        const response = await fetch(
          `/api/wilayah/villages/${this.selectedKecamatan.code}`,
          {
            signal: controller.signal,
            headers: {
              "Cache-Control": "no-cache",
              Pragma: "no-cache",
            },
          },
        );

        clearTimeout(timeoutId);

        if (!response.ok) {
          if (response.status === 502 && retryCount < 3) {
            await new Promise((resolve) =>
              setTimeout(resolve, 1000 * (retryCount + 1)),
            );
            return this.fetchKelurahan(retryCount + 1);
          }

          throw new Error(
            `Failed to fetch kelurahan: ${response.status} ${response.statusText}`,
          );
        }

        const data = (await response.json()) as { data?: RegionItem[] };

        if (!data.data || !Array.isArray(data.data)) {
          throw new Error("Invalid data format received");
        }

        this.kelurahanList = data.data;
        this.setCachedData("villages", data.data, this.selectedKecamatan.code);

        this.$nextTick(() => {
          const el = this.$refs.kelurahanInputEl as
            | HTMLInputElement
            | undefined;
          if (el) el.focus();
        });

        return data.data;
      } catch (error: any) {
        if (
          ((error?.name as string) === "AbortError" ||
            String(error?.message || "").includes("fetch")) &&
          retryCount < 2
        ) {
          await new Promise((resolve) => setTimeout(resolve, 2000));
          return this.fetchKelurahan(retryCount + 1);
        }

        this.kelurahanList = [];
        return [];
      } finally {
        this.isLoadingKelurahan = false;
      }
    },

    // Update address method to be more robust and prevent loops
    updateAlamat(this: ThisCtx) {
      if (this.isUpdating) return;
      if (this.isUserEditing) return;

      this.isUpdating = true;

      if (this.updateTimer) clearTimeout(this.updateTimer);

      let nomorPriority = false;
      try {
        nomorPriority =
          localStorage.getItem("alamat_nomor_priority") === "true";
      } catch {
        // no-op
      }

      let finalNomor = "";
      if (nomorPriority) {
        try {
          const storedNomor = localStorage.getItem("alamat_nomor");
          if (storedNomor) {
            finalNomor = storedNomor;
          }
        } catch {
          // no-op
        }
      }

      if (!finalNomor) {
        finalNomor = this.persistedNomor || this.nomor || "";
      }

      const currentJalan = this.persistedJalan || this.jalan || "";

      this.updateTimer = setTimeout(() => {
        const alamatParts: string[] = [];

        if (currentJalan.trim() !== "") {
          alamatParts.push(`Jl. ${currentJalan.trim()},`);
        }

        if (finalNomor.trim() !== "") {
          alamatParts.push(`No. ${finalNomor.trim()},`);
        }

        if (this.selectedKelurahan && this.kelurahanSelected) {
          const kelurahanName = (this.selectedKelurahan.name || "")
            .toString()
            .trim();
          if (kelurahanName) alamatParts.push(`Desa/Kel. ${kelurahanName},`);
        }

        if (this.selectedKecamatan && this.kecamatanSelected) {
          alamatParts.push(`Kec. ${this.selectedKecamatan.name},`);
        }

        if (this.selectedKabupaten?.name) {
          alamatParts.push(`${this.selectedKabupaten.name},`);
        }

        if (this.selectedProvinsi?.name) {
          alamatParts.push(`${this.selectedProvinsi.name}`);
        }

        if (alamatParts.length > 0) {
          const alamat = alamatParts.join(" ");

          if (finalNomor && !alamat.includes(`No. ${finalNomor}`)) {
            this.forceAddressUpdateWithNumber(finalNomor);
            return;
          }

          this.originalAddressState = {
            ...(this.originalAddressState || {
              provinsi: null,
              kabupaten: null,
              kecamatan: null,
              kelurahan: null,
              jalan: "",
              nomor: "",
            }),
            jalan: currentJalan,
            nomor: finalNomor,
            provinsi: this.selectedProvinsi
              ? JSON.stringify(this.selectedProvinsi)
              : null,
            kabupaten: this.selectedKabupaten
              ? JSON.stringify(this.selectedKabupaten)
              : null,
            kecamatan: this.selectedKecamatan
              ? JSON.stringify(this.selectedKecamatan)
              : null,
            kelurahan: this.selectedKelurahan
              ? JSON.stringify(this.selectedKelurahan)
              : null,
          };

          this.$emit("input", alamat);
          this.$emit("update:modelValue", alamat);
          this.lastEmittedAddress = alamat;
        }

        this.isUpdating = false;
      }, 300);
    },

    // Modify parseExistingAddress to better preserve user input
    parseExistingAddress(this: ThisCtx, addressString: string) {
      if (
        this.isUpdating ||
        this.isUserEditing ||
        !addressString ||
        typeof addressString !== "string"
      ) {
        return;
      }

      const currentJalan = this.jalan;
      const currentNomor = this.nomor;
      const hasUserSetJalan = !!(currentJalan && currentJalan.trim() !== "");
      const hasUserSetNomor = !!(currentNomor && currentNomor.trim() !== "");

      this.isUpdating = true;

      try {
        const parts = addressString.split(",").map((part) => part.trim());

        let persistedJalan = "";
        let persistedNomor = "";

        try {
          persistedJalan = localStorage.getItem("alamat_jalan") || "";
          persistedNomor = localStorage.getItem("alamat_nomor") || "";
        } catch {
          // no-op
        }

        if (persistedJalan) {
          this.jalan = persistedJalan;
          this.persistedJalan = persistedJalan;
        } else if (!hasUserSetJalan) {
          const first = parts[0]?.toLowerCase() || "";
          if (first.startsWith("jl.")) {
            this.jalan = (parts[0] || "").substring(3).trim();
          }
        }

        if (persistedNomor) {
          this.nomor = persistedNomor;
          this.persistedNomor = persistedNomor;
        } else if (!hasUserSetNomor) {
          const noPart = parts.find((p) =>
            (p || "").toLowerCase().includes("no."),
          );
          if (noPart) {
            this.nomor = noPart
              .substring(noPart.toLowerCase().indexOf("no.") + 3)
              .trim();
          }
        }

        const kecamatanPattern = /kec\.|kecamatan/i;
        const kecPart = parts.find((p) => p && kecamatanPattern.test(p));
        let kecamatanName = "";
        if (kecPart) {
          kecamatanName = (kecPart || "").replace(kecamatanPattern, "").trim();
        }

        const kelurahanPattern = /desa\/kel\.|desa|kel\.|kelurahan/i;
        const kelPart = parts.find((p) => p && kelurahanPattern.test(p));
        let kelurahanName = "";
        if (kelPart) {
          kelurahanName = (kelPart || "").replace(kelurahanPattern, "").trim();
        }

        const kabupatenPattern = /kab\.|kabupaten|kota/i;
        const kabPart = parts.find((p) => p && kabupatenPattern.test(p));
        let kabupatenName = "";
        if (kabPart) {
          kabupatenName = (kabPart || "").replace(kabupatenPattern, "").trim();
        }

        void this.fetchProvinsi()
          .then(async () => {
            const province = (parts[parts.length - 1] || "").trim();

            if (province && this.provinsiList.length > 0) {
              const matchingProvince = this.provinsiList.find(
                (p) => p.name.toLowerCase() === province.toLowerCase(),
              );

              if (matchingProvince) {
                this.selectedProvinsi = matchingProvince;
                this.inputValue = matchingProvince.name;
                this.provinsiSelected = true;

                if (kabupatenName) {
                  return this.fetchKabupaten().then(async () => {
                    if (this.kabupatenList.length > 0) {
                      const matchingKabupaten = this.kabupatenList.find(
                        (k) =>
                          kabupatenName
                            .toLowerCase()
                            .includes(k.name.toLowerCase()) ||
                          k.name
                            .toLowerCase()
                            .includes(kabupatenName.toLowerCase()),
                      );

                      if (matchingKabupaten) {
                        this.selectedKabupaten = matchingKabupaten;
                        this.kabupatenInput = matchingKabupaten.name;
                        this.kabupatenSelected = true;

                        if (kecamatanName) {
                          return this.fetchKecamatan().then(async () => {
                            if (this.kecamatanList.length > 0) {
                              const matchingKecamatan = this.kecamatanList.find(
                                (k) =>
                                  kecamatanName
                                    .toLowerCase()
                                    .includes(k.name.toLowerCase()) ||
                                  k.name
                                    .toLowerCase()
                                    .includes(kecamatanName.toLowerCase()),
                              );

                              if (matchingKecamatan) {
                                this.selectedKecamatan = matchingKecamatan;
                                this.kecamatanInput = matchingKecamatan.name;
                                this.kecamatanSelected = true;

                                if (kelurahanName) {
                                  return this.fetchKelurahan().then(() => {
                                    if (this.kelurahanList.length > 0) {
                                      let matchingKelurahan =
                                        this.kelurahanList.find(
                                          (k) =>
                                            (k.name || "")
                                              .toString()
                                              .trim()
                                              .toLowerCase() ===
                                            kelurahanName.toLowerCase(),
                                        );

                                      if (!matchingKelurahan) {
                                        matchingKelurahan =
                                          this.kelurahanList.find(
                                            (k) =>
                                              kelurahanName
                                                .toLowerCase()
                                                .includes(
                                                  (k.name || "")
                                                    .toString()
                                                    .trim()
                                                    .toLowerCase(),
                                                ) ||
                                              (k.name || "")
                                                .toString()
                                                .trim()
                                                .toLowerCase()
                                                .includes(
                                                  kelurahanName.toLowerCase(),
                                                ),
                                          );
                                      }

                                      if (matchingKelurahan) {
                                        this.selectedKelurahan = {
                                          ...matchingKelurahan,
                                          name: (matchingKelurahan.name || "")
                                            .toString()
                                            .trim(),
                                        };
                                        this.kelurahanInput = (
                                          matchingKelurahan.name || ""
                                        )
                                          .toString()
                                          .trim();
                                        this.kelurahanSelected = true;
                                      }
                                    }
                                  });
                                }
                              }
                            }
                          });
                        }
                      }
                    }
                  });
                }
              }
            }
          })
          .catch(() => {
            // swallow parse chain errors but ensure unlock
          })
          .finally(() => {
            this.originalAddressState = {
              jalan: this.jalan || "",
              nomor: this.nomor || "",
              provinsi: this.selectedProvinsi
                ? JSON.stringify(this.selectedProvinsi)
                : null,
              kabupaten: this.selectedKabupaten
                ? JSON.stringify(this.selectedKabupaten)
                : null,
              kecamatan: this.selectedKecamatan
                ? JSON.stringify(this.selectedKecamatan)
                : null,
              kelurahan: this.selectedKelurahan
                ? JSON.stringify(this.selectedKelurahan)
                : null,
            };

            setTimeout(() => {
              this.isUpdating = false;
            }, 1000);
          });
      } catch {
        this.isUpdating = false;
      }
    },

    forceAddressUpdate(this: ThisCtx) {
      let mostAccurateNomor = "";

      try {
        if (localStorage.getItem("alamat_nomor_priority") === "true") {
          mostAccurateNomor = localStorage.getItem("alamat_nomor") || "";
        }
      } catch {
        // no-op
      }

      if (!mostAccurateNomor) {
        mostAccurateNomor = this.persistedNomor || this.nomor || "";
      }

      if (mostAccurateNomor) {
        this.forceAddressUpdateWithNumber(mostAccurateNomor);
      } else {
        this.updateAlamat();
      }
    },

    // Add method to clear all cached address data
    clearStoredAddressData(this: ThisCtx) {
      try {
        localStorage.removeItem("alamat_jalan");
        localStorage.removeItem("alamat_jalan_temp");
        localStorage.removeItem("alamat_nomor");
        localStorage.removeItem("alamat_nomor_temp");
        localStorage.removeItem("alamat_nomor_priority");

        sessionStorage.removeItem("alamat_nomor_current");
      } catch {
        // no-op
      }
    },
  },
  created(this: ThisCtx) {
    // Clear stored address data on page reload (not on hot-module-replacement)
    const navEntries =
      (window.performance &&
        (window.performance.getEntriesByType(
          "navigation",
        ) as PerformanceNavigationTiming[])) ||
      [];
    const navType = navEntries[0]?.type;
    const isPageReload = navType === "navigate" || navType === "reload";

    if (isPageReload || document.readyState === "complete") {
      this.clearStoredAddressData();
    }

    if (this.value === undefined || this.value === null) {
      this.$emit("input", "");
      this.$emit("update:modelValue", "");
    }
  },
  mounted(this: ThisCtx) {
    void this.fetchProvinsi();

    setTimeout(() => {
      void this.prefetchPopularRegions();
    }, 1000);

    try {
      const lastLoad = sessionStorage.getItem("alamat_last_load_time");
      const currentTime = Date.now();

      if (!lastLoad || currentTime - Number.parseInt(lastLoad, 10) > 2000) {
        this.clearStoredAddressData();
      }

      sessionStorage.setItem("alamat_last_load_time", currentTime.toString());
    } catch {
      // no-op
    }

    this.originalAddressState = {
      jalan: this.jalan || "",
      nomor: this.nomor || "",
      provinsi: this.selectedProvinsi
        ? JSON.stringify(this.selectedProvinsi)
        : null,
      kabupaten: this.selectedKabupaten
        ? JSON.stringify(this.selectedKabupaten)
        : null,
      kecamatan: this.selectedKecamatan
        ? JSON.stringify(this.selectedKecamatan)
        : null,
      kelurahan: this.selectedKelurahan
        ? JSON.stringify(this.selectedKelurahan)
        : null,
    };

    this.$nextTick(() => {
      this.originalAddressState = {
        jalan: this.jalan,
        nomor: this.nomor,
        provinsi: this.selectedProvinsi
          ? JSON.stringify(this.selectedProvinsi)
          : null,
        kabupaten: this.selectedKabupaten
          ? JSON.stringify(this.selectedKabupaten)
          : null,
        kecamatan: this.selectedKecamatan
          ? JSON.stringify(this.selectedKecamatan)
          : null,
        kelurahan: this.selectedKelurahan
          ? JSON.stringify(this.selectedKelurahan)
          : null,
      };

      if (
        this.value &&
        ((this.selectedProvinsi &&
          !this.value.includes(this.selectedProvinsi.name)) ||
          (this.selectedKabupaten &&
            !this.value.includes(this.selectedKabupaten.name)) ||
          (this.selectedKecamatan &&
            !this.value.includes(this.selectedKecamatan.name)) ||
          (this.selectedKelurahan &&
            !this.value.includes(this.selectedKelurahan.name)))
      ) {
        this.forceAddressUpdate();
      }
    });

    try {
      const savedJalan = localStorage.getItem("alamat_jalan");
      const savedNomor = localStorage.getItem("alamat_nomor");

      if (savedJalan) {
        this.jalan = savedJalan;
        this.persistedJalan = savedJalan;
      }

      if (savedNomor) {
        this.nomor = savedNomor;
        this.persistedNomor = savedNomor;
      }
    } catch {
      // no-op
    }
  },

  // Add a beforeDestroy hook to compare and restore state if needed

  // For Vue 3 compatibility (in case the application is using Vue 3)
  beforeUnmount(this: ThisCtx) {
    this.cleanupTimers();

    let finalNomor = "";
    try {
      if (localStorage.getItem("alamat_nomor_priority") === "true") {
        finalNomor = localStorage.getItem("alamat_nomor") || "";
      }
    } catch {
      // no-op
    }

    if (!finalNomor) {
      finalNomor = this.persistedNomor || this.nomor || "";
    }

    if (finalNomor) {
      this.forceAddressUpdateWithNumber(finalNomor);
    } else {
      this.forceAddressUpdate();
    }
  },
});
</script>

<style scoped>
/* Reset styles for input and label elements */
:deep(.form-row label),
:deep(.form-row input),
:deep(.form-row .suggestions-container input),
:deep(.form-row select) {
  all: unset;
  box-sizing: border-box;
}

/* Form row styling */
:deep(.form-row) {
  display: flex;
  align-items: center;
  margin: 0 0 8px 0;
}

/* Label styling */
:deep(.form-row label) {
  min-width: 90px;
  margin-right: 6px;
  text-align: left;
  font-weight: bold;
}

/* Input and select styling */
:deep(.form-row input),
:deep(.form-row .suggestions-container input),
:deep(.form-row select) {
  flex: 1;
  width: 100%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

/* Suggestions container styling */
:deep(.suggestions-container) {
  position: relative;
  flex: 1;
  width: 100%;
}

/* Suggestions dropdown styling */
:deep(.suggestions) {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #ccc;
  border-top: none;
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
}

/* Suggestion item styling */
:deep(.suggestion-item) {
  padding: 8px;
  cursor: pointer;
}

:deep(.suggestion-item:hover) {
  background-color: #f0f0f0;
}

/* Loading indicator styling */
:deep(.loading-indicator) {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #ccc;
  border-top: none;
  z-index: 1000;
  padding: 8px;
  text-align: center;
}
</style>
