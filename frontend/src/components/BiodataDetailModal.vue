<template>
  <div v-if="show" class="modal-overlay" @click="emit('close')">
    <div class="modal-content" @click.stop>
      <div class="modal-header">
        <h3>Detail Biodata</h3>
        <button class="close-button" @click="emit('close')">&times;</button>
      </div>
      <div class="modal-body">
        <div v-if="data" class="detail-grid">
          <div class="detail-section">
            <h4>Informasi Pribadi</h4>
            <p>
              <strong>Nama <PERSON>kap:</strong>
              {{ data.nama_lengkap }}
            </p>
            <p>
              <strong>Nama Panggilan:</strong>
              {{ data.nama_panggilan }}
            </p>
            <p>
              <strong>Tempat Lahir:</strong>
              {{ data.kelahiran_tempat }}
            </p>
            <p>
              <strong>Tanggal Lahir:</strong>
              {{ formatDate(data.kelahiran_tanggal) }}
            </p>
            <p>
              <strong>Sekolah/Kelas:</strong>
              {{ data.sekolah }} / {{ data.kelas }}
            </p>
            <p>
              <strong>No. HP:</strong>
              {{ data.nomor_hape || "-" }}
            </p>
            <p>
              <strong>Jenis Kelamin:</strong>
              {{ data.jenis_kelamin }}
            </p>
            <p><strong>Hobi:</strong> {{ formatHobi(data.hobi) }}</p>
            <p><strong>Alamat:</strong> {{ formattedAlamat }}</p>
            <p>
              <strong>Tanggal Pendataan:</strong>
              {{ formatDate(data.created_at) }}
            </p>
          </div>
          <div class="detail-section">
            <h4>Informasi Keluarga</h4>
            <p><strong>Nama Ayah:</strong> {{ data.nama_ayah }}</p>
            <p><strong>Status Ayah:</strong> {{ data.status_ayah }}</p>
            <p>
              <strong>No. HP Ayah:</strong>
              {{ data.nomor_hape_ayah || "-" }}
            </p>
            <p><strong>Nama Ibu:</strong> {{ data.nama_ibu }}</p>
            <p><strong>Status Ibu:</strong> {{ data.status_ibu }}</p>
            <p>
              <strong>No. HP Ibu:</strong>
              {{ data.nomor_hape_ibu || "-" }}
            </p>
          </div>
          <div class="detail-section">
            <h4>Sambung</h4>
            <p>
              <strong>Sambung Desa:</strong>
              {{ data.sambung_desa }}
            </p>
            <p>
              <strong>Sambung Kelompok:</strong>
              {{ data.sambung_kelompok }}
            </p>
          </div>
          <div class="detail-section photo-section" v-if="data.foto_filename">
            <h4>Foto</h4>
            <img
              v-if="photoUrl"
              :src="photoUrl"
              :alt="`Foto ${data.nama_lengkap}`"
              class="biodata-photo"
              @error="handleImageError"
            />
            <div v-else-if="loadingPhoto" class="loading-photo">
              <p>Memuat foto...</p>
            </div>
            <p v-else class="no-photo">Foto tidak tersedia</p>
          </div>
          <div class="detail-section" v-else>
            <h4>Foto</h4>
            <p class="no-photo">Foto tidak tersedia</p>
          </div>
        </div>
        <div v-else class="loading-content">
          <p>Memuat detail...</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import * as Vue from "vue";
const { computed, onBeforeUnmount, ref, watch } = Vue;
import type { DetailedBiodataRecord } from "../types/biodata";

interface Props {
  show: boolean;
  data: DetailedBiodataRecord | null;
  apiKey: string;
}
const props = defineProps<Props>();
const emit = defineEmits<{ (e: "close"): void }>();

const photoUrl = ref<string | null>(null);
const loadingPhoto = ref<boolean>(false);

const formattedAlamat = computed<string>(() => {
  if (!props.data) return "";
  return props.data.alamat_tinggal || "-";
});
async function loadPhoto(filename: string): Promise<void> {
  loadingPhoto.value = true;
  if (photoUrl.value) {
    URL.revokeObjectURL(photoUrl.value);
    photoUrl.value = null;
  }

  const photoUrlPath = `/api/biodata/generus/foto/${filename}`;

  try {
    const response = await fetch(photoUrlPath, {
      headers: {
        Authorization: `ApiKey ${props.apiKey}`,
      },
    });

    if (!response.ok) {
      throw new Error(
        `HTTP error! status: ${response.status} - ${response.statusText}`,
      );
    }

    const blob = await response.blob();
    photoUrl.value = URL.createObjectURL(blob);
  } catch {
    photoUrl.value = null;
  } finally {
    loadingPhoto.value = false;
  }
}

function formatDate(dateString: string): string {
  if (!dateString) return "-";
  const date = new Date(dateString);
  return date.toLocaleDateString("id-ID", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
}

function formatHobi(hobi: unknown): string {
  if (!hobi) return "-";
  if (typeof hobi === "object" && hobi !== null) {
    return Object.values(hobi as Record<string, string>).join(", ") || "-";
  }
  return String(hobi);
}

function handleImageError(_event: Event): void {
  if (photoUrl.value) {
    URL.revokeObjectURL(photoUrl.value);
  }
  photoUrl.value = null;
}

watch(
  () => props.data,
  (newData: DetailedBiodataRecord | null) => {
    if (newData && newData.foto_filename) {
      void loadPhoto(newData.foto_filename);
    } else {
      if (photoUrl.value) {
        URL.revokeObjectURL(photoUrl.value);
      }
      photoUrl.value = null;
      loadingPhoto.value = false;
    }
  },
  { immediate: true },
);

watch(
  () => props.show,
  (newShow: boolean) => {
    if (!newShow && photoUrl.value) {
      URL.revokeObjectURL(photoUrl.value);
      photoUrl.value = null;
    }
  },
);

onBeforeUnmount(() => {
  if (photoUrl.value) {
    URL.revokeObjectURL(photoUrl.value);
    photoUrl.value = null;
  }
});
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.modal-content {
  background-color: white;
  border-radius: 15px;
  max-width: 95vw;
  max-height: 95vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  border: 2px solid #2e5a35;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  border-bottom: 2px solid #e9ecef;
  background-color: #f8f9fa;
  border-radius: 15px 15px 0 0;
}

.modal-header h3 {
  margin: 0;
  color: #2e5a35;
  font-size: 20px;
}

.close-button {
  background: #ff4444;
  color: white;
  border: none;
  font-size: 18px;
  cursor: pointer;
  width: 35px;
  height: 35px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  transition: all 0.2s;
}

.close-button:hover {
  background: #ff6666;
  transform: scale(1.1);
}

.modal-body {
  padding: 25px;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.detail-section {
  background-color: white;
  padding: 15px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.detail-section h4 {
  margin: 0 0 15px 0;
  color: #2e5a35;
  font-size: 16px;
  border-bottom: 2px solid #e9ecef;
  padding-bottom: 8px;
}

.detail-section p {
  margin: 8px 0;
  font-size: 14px;
  line-height: 1.4;
}

.detail-section strong {
  color: #495057;
}

.photo-section {
  text-align: center;
}

.biodata-photo {
  max-width: 200px;
  max-height: 250px;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  object-fit: cover;
}

.no-photo {
  color: #6c757d;
  font-style: italic;
  margin-top: 10px;
}

.loading-content {
  text-align: center;
  padding: 40px;
  color: #6c757d;
}

.loading-photo {
  text-align: center;
  padding: 20px;
  color: #6c757d;
  font-style: italic;
}

@media (max-width: 768px) {
  .modal-content {
    max-width: 95vw;
    max-height: 95vh;
  }

  .modal-header {
    padding: 15px 20px;
  }

  .modal-header h3 {
    font-size: 18px;
  }

  .modal-body {
    padding: 20px;
  }

  .close-button {
    width: 30px;
    height: 30px;
    font-size: 16px;
  }

  .detail-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .biodata-photo {
    max-width: 150px;
    max-height: 200px;
  }

  .detail-section {
    padding: 12px;
  }
}
</style>
