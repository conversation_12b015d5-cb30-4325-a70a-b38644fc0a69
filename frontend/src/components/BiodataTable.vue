<template>
    <div class="table-container">
        <table id="biodataTable">
            <thead>
                <tr>
                    <th>No.</th>
                    <th @click="$emit('sort', 'nama_lengkap')">
                        <PERSON><PERSON>
                        <span v-if="sortKey === 'nama_lengkap'">{{
                            sortOrder === "asc" ? "↑" : "↓"
                        }}</span>
                    </th>
                    <th @click="$emit('sort', 'nama_panggilan')">
                        <PERSON>a <PERSON>
                        <span v-if="sortKey === 'nama_panggilan'">{{
                            sortOrder === "asc" ? "↑" : "↓"
                        }}</span>
                    </th>
                    <th @click="$emit('sort', 'sambung_desa')">
                        Desa
                        <span v-if="sortKey === 'sambung_desa'">{{
                            sortOrder === "asc" ? "↑" : "↓"
                        }}</span>
                    </th>
                    <th @click="$emit('sort', 'sambung_kelompok')">
                        Kelompok
                        <span v-if="sortKey === 'sambung_kelompok'">{{
                            sortOrder === "asc" ? "↑" : "↓"
                        }}</span>
                    </th>
                    <th @click="$emit('sort', 'jenis_kelamin')">
                        Jenis Kelamin
                        <span v-if="sortKey === 'jenis_kelamin'">{{
                            sortOrder === "asc" ? "↑" : "↓"
                        }}</span>
                    </th>
                </tr>
            </thead>
            <tbody>
                <tr
                    v-for="(item, index) in data"
                    :key="index"
                    @click="$emit('row-click', item.biodata_id)"
                    class="clickable-row"
                >
                    <td>{{ index + 1 }}</td>
                    <td>{{ item.nama_lengkap }}</td>
                    <td>{{ item.nama_panggilan }}</td>
                    <td>{{ item.sambung_desa }}</td>
                    <td>{{ item.sambung_kelompok }}</td>
                    <td>{{ item.jenis_kelamin }}</td>
                </tr>
            </tbody>
        </table>
    </div>
</template>

<script setup lang="ts">
import type { BiodataRecord } from "../types/biodata";

interface Props {
    data: BiodataRecord[];
    sortKey: string;
    sortOrder: "asc" | "desc";
}

interface Emits {
    (e: "sort", key: string): void;
    (e: "row-click", id: number): void;
}

withDefaults(defineProps<Props>(), {
    sortKey: "",
    sortOrder: "asc",
});

defineEmits<Emits>();
</script>
