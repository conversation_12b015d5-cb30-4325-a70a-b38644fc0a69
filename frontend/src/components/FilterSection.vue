<template>
    <section class="filter-section">
        <div class="filter-item">
            <label for="desaFilter">Filter Desa:</label>
            <select
                v-model="localFilters.sambung_desa"
                @change="emitFilterChange"
                id="desaFilter"
            >
                <option value="">Semua</option>
                <option
                    v-for="desa in uniqueDesa"
                    :key="desa"
                    :value="desa"
                >
                    {{ desa }}
                </option>
            </select>
        </div>
        <div class="filter-item">
            <label for="kelompokFilter">Filter Kelompok:</label>
            <select
                v-model="localFilters.sambung_kelompok"
                @change="emitFilterChange"
                id="kelompokFilter"
            >
                <option value="">Semua</option>
                <option
                    v-for="kelompok in uniqueKelompok"
                    :key="kelompok"
                    :value="kelompok"
                >
                    {{ kelompok }}
                </option>
            </select>
        </div>
        <div class="filter-item">
            <label for="namaFilter">Filter Nama:</label>
            <input
                type="text"
                v-model="localFilters.nama"
                @input="emitFilterChange"
                placeholder="Cari nama..."
                id="namaFilter"
            />
        </div>
    </section>
</template>

<script lang="ts">
import { defineComponent, PropType } from "vue";
import type { Filters } from "../types/biodata";

export default defineComponent({
    name: "FilterSection",
    props: {
        filters: { type: Object as PropType<Filters>, required: true },
        uniqueDesa: { type: Array as PropType<string[]>, required: true },
        uniqueKelompok: { type: Array as PropType<string[]>, required: true },
    },
    emits: ["filter-change"],
    data() {
        return {
            localFilters: {
                ...(this as unknown as { filters: Filters }).filters,
            } as Filters,
        };
    },
    watch: {
        filters: {
            deep: true,
            handler(this: { localFilters: Filters }, newFilters: Filters) {
                this.localFilters = { ...newFilters };
            },
        },
    },
    methods: {
        emitFilterChange(): void {
            this.$emit("filter-change", { ...this.localFilters });
        },
    },
});
</script>
