<template>
    <div class="form-group">
        <label for="hobi"><PERSON><PERSON> (pilih satu atau lebih)</label>
        <div class="suggestions-container">
            <div class="selected-hobi-container" v-if="selectedHobi.length > 0">
                <div
                    v-for="(item, index) in selectedHobi"
                    :key="index"
                    class="hobi-tag"
                >
                    {{ item.kategori }}: {{ item.hobi }}
                    <span class="remove-hobi" @click="removeHobi(index)"
                        >&times;</span
                    >
                </div>
            </div>
            <input
                id="hobi"
                ref="inputEl"
                type="text"
                v-model="inputValue"
                @input="handleInput"
                @focus="handleFocus"
                @blur="handleBlur"
                @keyup="handleKeyup"
                @compositionstart="handleCompositionStart"
                @compositionend="handleCompositionEnd"
                placeholder="Ketik untuk mencari hobi"
            />
            <div
                class="suggestions"
                v-if="showSuggestions && filteredHobi.length"
            >
                <div
                    v-for="(item, index) in filteredHobi"
                    :key="index"
                    class="suggestion-item"
                    @click.stop="selectHobi(item)"
                >
                    <strong>{{ item.kategori }}</strong
                    >: {{ item.hobi }}
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, PropType } from "vue";
import type { HobiItem } from "../types/biodata";

export default defineComponent({
    name: "HobiSelector",
    props: {
        hobiOptions: { type: Array as PropType<HobiItem[]>, required: true },
        selectedHobi: { type: Array as PropType<HobiItem[]>, required: true },
        placeholder: { type: String, default: "Ketik untuk mencari hobi" },
    },
    emits: {
        "update:selectedHobi": (_payload: HobiItem[]) => true,
    },
    data() {
        return {
            inputValue: "" as string,
            showSuggestions: false as boolean,
            isComposing: false as boolean,
        };
    },
    computed: {
        filteredHobi(): HobiItem[] {
            const searchTerm = this.inputValue.toLowerCase();
            if (!searchTerm) return [];
            return this.hobiOptions.filter((item: HobiItem) => {
                const detail = item.detail_hobi?.toLowerCase() || "";
                return (
                    item.kategori.toLowerCase().includes(searchTerm) ||
                    item.hobi.toLowerCase().includes(searchTerm) ||
                    detail.includes(searchTerm)
                );
            });
        },
    },
    methods: {
        handleInput(): void {
            this.showSuggestions = true;
        },
        handleFocus(): void {
            if (this.inputValue) {
                this.showSuggestions = true;
            }
        },
        handleBlur(): void {
            setTimeout(() => {
                this.showSuggestions = false;
            }, 150);
        },
        handleKeyup(_event: KeyboardEvent): void {
            // Optional: Implement keyboard interactions if needed
        },
        handleCompositionStart(): void {
            this.isComposing = true;
        },
        handleCompositionEnd(event: CompositionEvent): void {
            this.isComposing = false;
            const target = event.target as HTMLInputElement;
            this.inputValue = target.value;
            this.handleInput();
        },
        selectHobi(item: HobiItem): void {
            const current = this.selectedHobi as unknown as HobiItem[];
            const isDuplicate = current.some(
                (selectedItem: HobiItem) =>
                    selectedItem.kategori === item.kategori &&
                    selectedItem.hobi === item.hobi,
            );

            const next = isDuplicate
                ? current.slice()
                : [
                      ...current,
                      {
                          id: item.id,
                          kategori: item.kategori,
                          hobi: item.hobi,
                          detail_hobi: item.detail_hobi,
                      } as HobiItem,
                  ];

            this.inputValue = "";
            this.showSuggestions = false;
            this.$emit("update:selectedHobi", next);
        },
        removeHobi(index: number): void {
            const current = this.selectedHobi as unknown as HobiItem[];
            const next = current.filter((_, i) => i !== index);
            this.$emit("update:selectedHobi", next);
        },
    },
});
</script>
