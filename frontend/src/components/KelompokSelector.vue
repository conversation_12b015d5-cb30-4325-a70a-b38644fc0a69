<template>
    <div class="form-group">
        <label for="kelompok">Alamat Sambung</label>
        <div class="suggestions-container">
            <input
                id="kelompok"
                ref="inputEl"
                type="text"
                v-model="inputValue"
                @input="handleInput"
                @focus="handleFocus"
                @blur="handleBlur"
                @keyup="handleKeyup"
                @compositionstart="handleCompositionStart"
                @compositionend="handleCompositionEnd"
                :placeholder="placeholder"
                required
            />
            <div v-if="isLoading" class="loading-indicator">Loading...</div>
            <div v-if="loadError" class="error-message">{{ loadError }}</div>
            <div
                class="suggestions"
                v-if="showSuggestions && filteredKelompok.length"
            >
                <div
                    v-for="(item, index) in filteredKelompok"
                    :key="index"
                    class="suggestion-item"
                    @click="selectKelompok(item)"
                >
                    {{ item.kelompok }} ({{ item.desa }})
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import * as Vue from "vue";
import type {
    FormData,
    KelompokOptions,
    FlattenedKelompok,
} from "../types/biodata";

interface Props {
    formData: FormData;
    kelompokOptions: KelompokOptions;
    flattenedKelompok: FlattenedKelompok[];
    dataLoaded: boolean;
    isLoading: boolean;
    loadError: string | null;
    placeholder?: string;
}

const props = withDefaults(defineProps<Props>(), {
    placeholder: "Ketik untuk mencari kelompok atau desa.",
});

const {
    formData,
    flattenedKelompok,
    dataLoaded,
    isLoading,
    loadError,
    placeholder,
} = Vue.toRefs(props);

const emit = defineEmits<{
    (e: "input-change"): void;
}>();

const inputValue = Vue.ref<string>("");
const showSuggestions = Vue.ref<boolean>(false);
const isComposing = Vue.ref<boolean>(false);

const filteredKelompok = Vue.computed<FlattenedKelompok[]>(() => {
    const searchTerm = inputValue.value.toLowerCase();
    if (!searchTerm || !dataLoaded.value) return [];

    const seen = new Set<string>();
    return flattenedKelompok.value.filter(({ kelompok, desa }) => {
        const identifier = `${kelompok.toLowerCase()}-${desa.toLowerCase()}`;
        const matches =
            kelompok.toLowerCase().includes(searchTerm) ||
            desa.toLowerCase().includes(searchTerm);
        if (matches && !seen.has(identifier)) {
            seen.add(identifier);
            return true;
        }
        return false;
    });
});

function handleInput(): void {
    showSuggestions.value = true;
    formData.value.sambung_desa = "";
    formData.value.sambung_kelompok = "";
    emit("input-change");
}

function handleFocus(): void {
    if (inputValue.value) {
        showSuggestions.value = true;
    }
}

function handleBlur(): void {
    setTimeout(() => {
        showSuggestions.value = false;
    }, 150);
}

function handleKeyup(_event: KeyboardEvent): void {
    // Optional: Implement keyboard interactions if needed
}

function handleCompositionStart(): void {
    isComposing.value = true;
}

function handleCompositionEnd(event: CompositionEvent): void {
    isComposing.value = false;
    const target = event.target as HTMLInputElement;
    inputValue.value = target.value;
    handleInput();
}

function selectKelompok(payload: FlattenedKelompok): void {
    const { kelompok, desa } = payload;
    inputValue.value = `${kelompok} (${desa})`;
    formData.value.sambung_kelompok = kelompok;
    formData.value.sambung_desa = desa;
    showSuggestions.value = false;
}
</script>
