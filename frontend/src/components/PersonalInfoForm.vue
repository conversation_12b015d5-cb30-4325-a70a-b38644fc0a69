<template>
    <div>
        <div class="section-title">Data Pribadi</div>
        <label for="nama_lengkap"><PERSON><PERSON></label>
        <input
            id="nama_lengkap"
            type="text"
            v-model="formData.nama_lengkap"
            @input="convertToUppercase('nama_lengkap', $event)"
            placeholder="Nama <PERSON>"
            required
        />
        <label for="nama_panggilan">Nama <PERSON></label>
        <input
            id="nama_panggilan"
            type="text"
            v-model="formData.nama_panggilan"
            @input="convertToUppercase('nama_panggilan', $event)"
            placeholder="Nama Panggilan"
            required
        />
        <div class="form-group">
            <label for="jenis_kelamin">Jen<PERSON></label>
            <select
                id="jenis_kelamin"
                v-model="formData.jenis_kelamin"
                required
            >
                <option value="" disabled selected><PERSON><PERSON><PERSON></option>
                <option value="LAKI-LAKI">LAKI-LAKI</option>
                <option value="PEREMPUAN">PEREMPUAN</option>
            </select>
        </div>
        <div class="form-group">
            <label for="kelahiran_tempat">Tempat Lahir</label>
            <input
                id="kelahiran_tempat"
                type="text"
                v-model="formData.kelahiran_tempat"
                @input="convertToUppercase('kelahiran_tempat', $event)"
                placeholder="Tempat Lahir"
                required
            />
        </div>
        <div class="form-group">
            <label for="kelahiran_tanggal">Tanggal Lahir</label>
            <input
                id="kelahiran_tanggal"
                type="date"
                v-model="formData.kelahiran_tanggal"
                required
            />
        </div>
        <div class="form-group">
            <label for="alamat_tinggal">Alamat Tinggal (Lengkap)</label>
            <div class="alamat-tinggal-box">
                <AlamatTinggalForm
                    id="alamat_tinggal"
                    :value="formData.alamat_tinggal"
                    @input="updateAlamatTinggal"
                />
            </div>
        </div>
        <div class="form-group">
            <label for="nomor_hape">Nomor HP</label>
            <input
                id="nomor_hape"
                type="tel"
                v-model="formData.nomor_hape"
                placeholder="Nomor HP"
            />
        </div>
    </div>
</template>

<script setup lang="ts">
import * as Vue from "vue";
import AlamatTinggalForm from "./AlamatTinggalForm.vue";
import type { FormData } from "../types/biodata";

interface Props {
    formData: FormData;
}

const props = defineProps<Props>();

const convertToUppercase = (fieldName: keyof FormData, event: Event): void => {
    const target = event.target as HTMLInputElement;
    const uppercaseValue = target.value.toUpperCase();
    props.formData[fieldName] = uppercaseValue;
    target.value = uppercaseValue;
};

const updateAlamatTinggal = (value: string | Event): void => {
    let stringValue = "";

    if (value === null || value === undefined) {
        console.log("PersonalInfoForm received null/undefined alamat_tinggal");
        stringValue = "";
    } else if (
        value &&
        typeof value === "object" &&
        "target" in (value as any) &&
        (value as any).target &&
        "value" in ((value as any).target as any)
    ) {
        stringValue = ((value as any).target as HTMLInputElement).value;
    } else if (
        value &&
        typeof value === "object" &&
        ((value as any).constructor?.name === "InputEvent" ||
            value instanceof Event)
    ) {
        console.log("PersonalInfoForm received InputEvent, ignoring");
        return;
    } else if (typeof value !== "string") {
        console.log(
            "PersonalInfoForm received non-string value, converting:",
            value,
        );
        try {
            stringValue = String(value);
        } catch (e) {
            console.error("Failed to convert value to string:", e);
            return;
        }
    } else {
        stringValue = value;
    }

    console.log("PersonalInfoForm updating alamat_tinggal to:", stringValue);

    if (props.formData.alamat_tinggal !== stringValue) {
        props.formData.alamat_tinggal = stringValue;
    }
};

Vue.watch(
    () => props.formData.alamat_tinggal,
    (newVal: string) => {
        console.log("PersonalInfoForm detected alamat_tinggal change:", newVal);
    },
);

Vue.onMounted(() => {
    console.log(
        "PersonalInfoForm mounted with alamat_tinggal:",
        props.formData.alamat_tinggal,
    );

    if (
        props.formData.alamat_tinggal === null ||
        props.formData.alamat_tinggal === undefined
    ) {
        props.formData.alamat_tinggal = "";
        console.log("Initialized empty alamat_tinggal");
    }

    Vue.nextTick(() => {
        // This ensures any child components get the updated value
    });
});
</script>
<style scoped>
.alamat-tinggal-box {
    border: 1px solid #ccc;
    border-radius: 8px;
    padding: 6px;
    margin-top: 8px;
    background-color: #f9f9f9;
}

#alamat_tinggal {
    width: 100%;
    margin: 0;
    padding: 0;
}
</style>
