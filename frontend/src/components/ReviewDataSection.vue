<template>
  <div class="review-data">
    <div class="section-title">Review Data</div>
    <div class="review-item">
      <strong><PERSON><PERSON>:</strong> {{ formData.nama_lengkap }}
    </div>
    <div class="review-item">
      <strong><PERSON><PERSON>:</strong> {{ formData.nama_panggilan }}
    </div>
    <div class="review-item">
      <strong><PERSON><PERSON>:</strong> {{ formData.jenis_kelamin }}
    </div>
    <div class="review-item">
      <strong>Tempat, Tanggal Lahir:</strong>
      {{ formData.kelahiran_tempat }},
      {{ formatDate(formData.kelahiran_tanggal) }}
    </div>
    <div class="review-item">
      <strong>Alamat Tinggal:</strong>
      {{ formData.alamat_tinggal || "Tidak ada" }}
    </div>
    <div class="review-item">
      <strong>Nomor HP:</strong> {{ formData.nomor_hape || "-" }}
    </div>
    <div class="review-item" v-if="selectedPhoto">
      <strong>Foto:</strong>
      <div class="photo-preview">
        <img :src="photoPreviewUrl" alt="Preview foto" class="review-photo" />
      </div>
    </div>
    <div class="review-item">
      <strong>Desa/Kelompok:</strong> {{ formData.sambung_desa }} /
      {{ formData.sambung_kelompok }}
    </div>
    <div class="review-item">
      <strong>Sekolah & Kelas:</strong>
      {{ formData.sekolah_kelas || "-" }}
    </div>
    <div class="review-item" v-if="selectedHobi.length > 0">
      <strong>Hobi:</strong>
      <div
        v-for="(item, index) in selectedHobi"
        :key="index"
        class="review-hobi-item"
      >
        - {{ item.kategori }}: {{ item.hobi }}
      </div>
    </div>
    <div class="review-item">
      <strong>Nama Ayah:</strong> {{ formData.nama_ayah }}
    </div>
    <div class="review-item">
      <strong>Status Ayah:</strong> {{ formData.status_ayah }}
    </div>
    <div class="review-item">
      <strong>No. HP Ayah:</strong>
      {{ formData.nomor_hape_ayah || "-" }}
    </div>
    <div class="review-item">
      <strong>Nama Ibu:</strong> {{ formData.nama_ibu }}
    </div>
    <div class="review-item">
      <strong>Status Ibu:</strong> {{ formData.status_ibu }}
    </div>
    <div class="review-item">
      <strong>No. HP Ibu:</strong>
      {{ formData.nomor_hape_ibu || "-" }}
    </div>
    <div class="review-actions">
      <button @click="handleSubmit" :disabled="isSubmitting">
        {{ isSubmitting ? "Mengirim..." : "Kirim Data" }}
      </button>
      <button @click="handleEdit" class="secondary">Edit Data</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import * as Vue from "vue";
import type { FormData, HobiItem, EditFormData } from "../types/biodata";

interface Props {
  formData: FormData;
  selectedHobi?: HobiItem[];
  selectedPhoto?: File | null;
  isSubmitting?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  selectedHobi: () => [],
  selectedPhoto: null,
  isSubmitting: false,
});

const { formData, selectedHobi, selectedPhoto, isSubmitting } =
  Vue.toRefs(props);

const emit = defineEmits<{
  (e: "submit"): void;
  (e: "edit", payload: EditFormData): void;
}>();

const savedAddress = Vue.ref<string>("");

const photoPreviewUrl = Vue.computed<string | undefined>(() => {
  return selectedPhoto.value
    ? URL.createObjectURL(selectedPhoto.value)
    : undefined;
});

function formatDate(dateString: string): string {
  if (!dateString) return "";
  const options: Intl.DateTimeFormatOptions = {
    day: "numeric",
    month: "long",
    year: "numeric",
  };
  return new Date(dateString).toLocaleDateString("id-ID", options);
}

function handleSubmit(): void {
  emit("submit");
}

function handleEdit(): void {
  console.log(
    "ReviewDataSection - alamat_tinggal before edit:",
    formData.value.alamat_tinggal,
  );
  savedAddress.value = formData.value.alamat_tinggal || "";
  emit("edit", { savedAddress: savedAddress.value });
}

Vue.onMounted(() => {
  console.log(
    "ReviewDataSection mounted with alamat_tinggal:",
    formData.value.alamat_tinggal,
  );
  savedAddress.value = formData.value.alamat_tinggal || "";
});
</script>

<style scoped>
.photo-preview {
  margin-top: 10px;
}

.review-photo {
  max-width: 150px;
  max-height: 150px;
  border-radius: 8px;
  border: 2px solid #ddd;
  object-fit: cover;
}
</style>
