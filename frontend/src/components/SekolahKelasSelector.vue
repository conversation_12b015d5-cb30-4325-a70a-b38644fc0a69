<template>
    <div class="form-group">
        <label for="sekolah_kelas"
            ><PERSON>las <PERSON>lah / Kerja / Wirausaha / Muballigh</label
        >
        <div class="suggestions-container">
            <input
                id="sekolah_kelas"
                ref="inputEl"
                type="text"
                v-model="inputValue"
                @input="handleInput"
                @focus="handleFocus"
                @blur="handleBlur"
                @keyup="handleKeyup"
                @compositionstart="handleCompositionStart"
                @compositionend="handleCompositionEnd"
                :placeholder="placeholder"
            />
            <div
                class="suggestions"
                v-if="showSuggestions && filteredSekolahKelas.length"
            >
                <div
                    v-for="(item, index) in filteredSekolahKelas"
                    :key="index"
                    class="suggestion-item"
                    @click="selectSekolahKelas(item)"
                >
                    {{ item.jenjang }} {{ item.kelas }}
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import * as Vue from "vue";
import type { FormData, SekolahKelasOption } from "../types/biodata";

interface Props {
    formData: FormData;
    sekolahKelasOptions: SekolahKelasOption[];
    placeholder?: string;
}

const props = withDefaults(defineProps<Props>(), {
    placeholder: "Ketik saja ...",
});

const emit = defineEmits<{
    (e: "input-change"): void;
}>();

const inputValue = Vue.ref<string>("");
const showSuggestions = Vue.ref<boolean>(false);
const isComposing = Vue.ref<boolean>(false);

const filteredSekolahKelas = Vue.computed<SekolahKelasOption[]>(() => {
    const searchTerm = inputValue.value.toLowerCase();
    return searchTerm
        ? props.sekolahKelasOptions.filter(
              ({ jenjang, kelas }: SekolahKelasOption) =>
                  jenjang.toLowerCase().includes(searchTerm) ||
                  kelas.toLowerCase().includes(searchTerm),
          )
        : props.sekolahKelasOptions;
});

function handleInput(): void {
    showSuggestions.value = true;
    props.formData.sekolah_kelas = ""; // Clear selection on input
    emit("input-change");
}
function handleFocus(): void {
    if (inputValue.value) {
        showSuggestions.value = true;
    }
}
function handleBlur(): void {
    setTimeout(() => {
        showSuggestions.value = false;
    }, 150);
}
function handleKeyup(_event: KeyboardEvent): void {
    // Optional: keyboard interactions can be implemented here
}
function handleCompositionStart(): void {
    isComposing.value = true;
}
function handleCompositionEnd(event: CompositionEvent): void {
    isComposing.value = false;
    const target = event.target as HTMLInputElement;
    inputValue.value = target.value;
    handleInput();
}
function selectSekolahKelas({ jenjang, kelas }: SekolahKelasOption): void {
    const formattedValue = `${jenjang} ${kelas}`;
    inputValue.value = formattedValue;
    props.formData.sekolah_kelas = formattedValue;
    showSuggestions.value = false;
}
</script>
