import type {
  DataState,
  KelompokOptions,
  HobiItem,
  FlattenedKelompok,
  EditFormData,
} from "../types/biodata";

interface ApiResponseData {
  data?: any;
  [key: string]: any;
}

interface PreselectedItem {
  desa: string;
  kelompok: string;
}

export default {
  methods: {
    async fetchDataFromApi<T>(
      url: string,
      stateObj: DataState<T>,
      processDataFn?: (data: any) => T,
      errorMessage?: string,
      retryFn?: () => void,
    ): Promise<T | null> {
      console.log(`Fetching data from ${url}...`);
      try {
        const response = await fetch(url);
        if (!response.ok) {
          throw new Error("Network response was not ok");
        }

        const data: ApiResponseData = await response.json();

        // Handle API response structure - extract the data array if it exists
        const extractedData = data.data || data;

        // Process data with custom function if provided
        const processedData = processDataFn
          ? processDataFn(extractedData)
          : extractedData;

        // Update state based on stateObj reference
        stateObj.options = processedData;
        stateObj.isLoading = false;
        stateObj.dataLoaded = true;
        stateObj.loadError = null;

        console.log(`Data loaded successfully from ${url}`);
        return processedData;
      } catch (error) {
        console.error(`Error fetching data from ${url}:`, error);
        stateObj.loadError =
          errorMessage ?? "Gagal memuat data. Silakan muat ulang halaman.";
        stateObj.isLoading = false;
        stateObj.dataLoaded = false;

        // Retry after delay if retry function provided
        if (typeof retryFn === "function") {
          setTimeout(() => retryFn(), 5000);
        }
        return null;
      }
    },

    async fetchKelompokData(dataParam: string | null): Promise<void> {
      if (!dataParam) {
        console.error("Parameter data tidak ditemukan");
        (this as any).kelompokData.loadError = "Parameter data tidak ditemukan";
        (this as any).kelompokData.isLoading = false;
        return;
      }

      const encodedParam = encodeURIComponent(dataParam);
      const url = `/api/data/daerah/${encodedParam}/`;

      const processData = (data: any[]): KelompokOptions => {
        const formattedData: KelompokOptions = {};
        let preselectedItem: PreselectedItem | null = null;

        // Ensure data is an array (it should be after extraction in fetchDataFromApi)
        const dataArray = Array.isArray(data) ? data : [];

        // Format the data into a nested structure (ranah -> detail_ranah)
        dataArray.forEach((item: any) => {
          // Map ranah to desa and detail_ranah to kelompok
          const list =
            formattedData[item.ranah] ?? (formattedData[item.ranah] = []);
          list.push(item.detail_ranah);

          // Check if this item matches our URL parameter
          if (
            dataParam &&
            (item.ranah.toLowerCase().includes(dataParam.toLowerCase()) ||
              item.detail_ranah.toLowerCase().includes(dataParam.toLowerCase()))
          ) {
            preselectedItem = {
              desa: item.ranah,
              kelompok: item.detail_ranah,
            };
          }
        });

        // If we found a match for the URL parameter, pre-select it
        if (preselectedItem) {
          setTimeout(() => {
            if (preselectedItem && (this as any).formData) {
              (this as any).formData.sambung_desa = preselectedItem.desa;
              (this as any).formData.sambung_kelompok =
                preselectedItem.kelompok;
            }
          }, 0);
        }

        return formattedData;
      };

      await this.fetchDataFromApi(
        url,
        (this as any).kelompokData,
        processData,
        "Gagal memuat data kelompok. Silakan muat ulang halaman.",
        () => this.fetchKelompokData(dataParam),
      );
    },

    async fetchHobiData(): Promise<void> {
      await this.fetchDataFromApi(
        "/api/data/hobi",
        (this as any).hobiData,
        undefined,
        "Gagal memuat data hobi. Silakan muat ulang halaman.",
        () => this.fetchHobiData(),
      );
    },

    async fetchSekolahKelasData(): Promise<void> {
      try {
        const response = await fetch("/api/data/kelas-sekolah");
        if (!response.ok) {
          throw new Error("Network response was not ok");
        }
        const data: ApiResponseData = await response.json();
        // Handle API response structure - extract the data array if it exists
        (this as any).sekolahKelasOptions = data.data || data;
      } catch (error) {
        console.error("Error fetching sekolah/kelas data:", error);
      }
    },

    validateFormFields(): boolean {
      const formData = (this as any).formData;
      if (!formData) {
        return false;
      }

      const { sambung_kelompok, sambung_desa } = formData;
      if (!sambung_kelompok || !sambung_desa) {
        alert(
          "Pilihan Desa & Kelompok wajib diisi. Silakan ketik dan pilih dari daftar yang muncul.",
        );
        return false;
      }

      const flattenedKelompok = (this as any).flattenedKelompok;
      if (!flattenedKelompok) {
        return false;
      }

      const isValidKelompok = flattenedKelompok.some(
        (item: FlattenedKelompok) =>
          item.kelompok === sambung_kelompok && item.desa === sambung_desa,
      );

      if (!isValidKelompok) {
        alert(
          "Silahkan pilih kelompok sesuai dengan pilihan yang muncul saat Anda mengetik",
        );
        return false;
      }
      return true;
    },

    showReviewData(): void {
      if (!this.validateFormFields()) {
        return;
      }
      (this as any).showReview = true;
    },

    editForm(data?: EditFormData): void {
      const formData = (this as any).formData;
      if (!formData) {
        return;
      }

      // Explicitly log the alamat_tinggal before toggling to help debug
      console.log(
        "EditForm called with alamat_tinggal:",
        formData.alamat_tinggal,
      );

      // Save current address if available
      const savedAddress =
        data && data.savedAddress ? data.savedAddress : formData.alamat_tinggal;

      // Toggle the review state
      (this as any).showReview = false;

      // Force a refresh of the component tree and restore address if needed
      const nextTick = (this as any).$nextTick;
      if (nextTick) {
        nextTick(() => {
          if (
            savedAddress &&
            formData &&
            (!formData.alamat_tinggal ||
              formData.alamat_tinggal !== savedAddress)
          ) {
            console.log("Restoring alamat_tinggal to:", savedAddress);
            formData.alamat_tinggal = savedAddress;
          }
          console.log(
            "After toggling, alamat_tinggal is:",
            formData?.alamat_tinggal,
          );
        });
      }
    },

    async submitToAPI(): Promise<void> {
      if ((this as any).isSubmitting) return;

      console.log("=== SUBMISSION DEBUG START ===");
      console.log("API Key present:", !!(this as any).apiKey);

      const formData = (this as any).formData;
      if (!formData) {
        console.error("Form data is not available");
        return;
      }

      // Check for required fields again before submission
      if (!formData.sambung_desa || !formData.sambung_kelompok) {
        console.error("Missing required Desa/Kelompok fields");
        alert("Pilihan Desa & Kelompok wajib diisi sebelum mengirim data.");
        return;
      }

      // Check for API key
      if (!(this as any).apiKey) {
        console.error("API key missing, aborting submission");
        alert("API key diperlukan. Silakan gunakan URL dengan parameter key.");
        (this as any).showApiKeyError = true;
        return;
      }

      (this as any).isSubmitting = true;
      console.log("Starting form submission process");
      console.log("Original form data:", JSON.stringify(formData));
      console.log("Selected hobi items:", (this as any).selectedHobi);
      console.log("CRITICAL FIELDS CHECK:");
      console.log("sambung_desa:", formData.sambung_desa);
      console.log("sambung_kelompok:", formData.sambung_kelompok);

      try {
        const formDataObj = new FormData();
        console.log("Created new FormData object");

        // Add the critical sambung fields first to ensure they're included
        formDataObj.append("sambung_desa", formData.sambung_desa);
        formDataObj.append("sambung_kelompok", formData.sambung_kelompok);
        console.log("Added sambung fields to FormData");

        // Process hobi data as a properly formatted JSON string
        const selectedHobi = (this as any).selectedHobi;
        if (selectedHobi && selectedHobi.length > 0) {
          console.log(
            "Processing hobi data, found",
            selectedHobi.length,
            "selected items",
          );

          // Group hobi items by category
          const groupedHobi: { [key: string]: string[] } = {};
          selectedHobi.forEach((item: HobiItem) => {
            const list =
              groupedHobi[item.kategori] ?? (groupedHobi[item.kategori] = []);
            list.push(item.hobi);
          });

          // Convert arrays to strings for API compatibility
          const hobiObj: { [key: string]: string } = {};
          for (const [kategori, hobiList] of Object.entries(groupedHobi)) {
            hobiObj[kategori] = hobiList.join(", ");
          }

          // Convert to JSON string as required by backend
          const hobiJson = JSON.stringify(hobiObj);
          formDataObj.append("hobi", hobiJson);
          console.log("Added hobi JSON string to FormData");
        } else {
          formDataObj.append("hobi", JSON.stringify({}));
          console.log("No hobi selected, adding empty JSON object");
        }

        // Add all other form fields to the FormData object
        console.log("Adding other form fields to FormData");
        for (const key in formData) {
          // Skip already handled fields
          if (["hobi", "sambung_desa", "sambung_kelompok"].includes(key)) {
            continue;
          }

          // Only add non-empty values for optional fields
          if (
            formData[key] !== null &&
            formData[key] !== undefined &&
            formData[key] !== ""
          ) {
            const value = formData[key];
            if (value instanceof Blob) {
              formDataObj.append(key, value);
            } else {
              formDataObj.append(key, String(value));
            }
          }
        }

        // Send data to the API endpoint with Authorization header
        const apiUrl = "/api/biodata/generus";
        console.log("Sending request to:", apiUrl);

        const response = await fetch(apiUrl, {
          method: "POST",
          headers: {
            Authorization: "ApiKey " + (this as any).apiKey,
          },
          body: formDataObj,
        });

        console.log("Response status:", response.status, response.statusText);

        if (!response.ok) {
          const errorText = await response.text();
          console.error("Error response from server:", errorText);
          try {
            const errorJson = JSON.parse(errorText);
            console.error("Parsed error response:", errorJson);
          } catch (e) {
            console.log("Error response is not valid JSON");
          }
          throw new Error(`Server error: ${response.status} - ${errorText}`);
        }

        const responseData = await response.json();
        console.log("Success response:", responseData);

        // Show success message
        (this as any).showReview = false;
        (this as any).showSuccess = true;
        console.log("Form submitted successfully");
      } catch (error) {
        console.error("Error in submission process:", error);
        console.error("Error stack:", (error as Error).stack);
        alert("Terjadi kesalahan saat mengirim data. Mohon coba lagi.");
      } finally {
        (this as any).isSubmitting = false;
        console.log("=== SUBMISSION DEBUG END ===");
      }
    },

    resetForm(): void {
      // Reload the page to completely reset all form data and state
      window.location.reload();
    },

    getCurrentDate(): string {
      const today = new Date();
      const year = today.getFullYear();
      const month = String(today.getMonth() + 1).padStart(2, "0");
      const day = String(today.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    },

    formatDate(dateString: string): string {
      if (!dateString) return "";
      const options: Intl.DateTimeFormatOptions = {
        day: "numeric",
        month: "long",
        year: "numeric",
      };
      return new Date(dateString).toLocaleDateString("id-ID", options);
    },

    handleKelompokInputChange(): void {
      const kelompokData = (this as any).kelompokData;
      if (!kelompokData) {
        return;
      }

      // Retry fetching data if needed
      if (!kelompokData.dataLoaded && !kelompokData.isLoading) {
        console.log("Data not loaded, retrying fetch...");
        const dataParam = new URLSearchParams(window.location.search).get(
          "data",
        );
        this.fetchKelompokData(dataParam);
      }
    },
  },
};
