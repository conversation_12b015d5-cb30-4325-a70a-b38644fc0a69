export interface SuggestionsMixinData {
  inputValue: string;
  showSuggestions: boolean;
  isComposing: boolean;
}

export interface SuggestionsMixinMethods {
  handleInput(): void;
  handleFocus(): void;
  handleBlur(): void;
  handleKeyup(event: KeyboardEvent): void;
  handleCompositionEnd(event: CompositionEvent): void;
  handleCompositionStart(): void;
}

export default {
  props: {
    placeholder: {
      type: String,
      default: "Ketik untuk mencari...",
    },
  },

  data(): SuggestionsMixinData {
    return {
      inputValue: "",
      showSuggestions: false,
      isComposing: false,
    };
  },

  methods: {
    handleInput(this: any): void {
      this.showSuggestions = true;
      this.$emit("input-change", this.inputValue);
    },

    handleFocus(this: any): void {
      if (this.inputValue) {
        this.showSuggestions = true;
      }
    },

    handleBlur(this: any): void {
      // Delay hiding suggestions to allow click to register
      setTimeout(() => {
        this.showSuggestions = false;
      }, 150);
    },

    handleKeyup(this: any, _event: KeyboardEvent): void {
      const inputEl = this.$refs.inputEl as HTMLInputElement;
      if (inputEl && this.inputValue !== inputEl.value) {
        this.inputValue = inputEl.value;
        this.handleInput();
      }
    },

    handleCompositionEnd(this: any, event: CompositionEvent): void {
      this.isComposing = false;
      const target = event.target as HTMLInputElement;
      this.inputValue = target.value;
      this.handleInput();
    },

    handleCompositionStart(this: any): void {
      this.isComposing = true;
    },
  },
};
