<template>
    <div class="pantau-biodata-container">
        <h1>Pantauan Biodata Generus</h1>
        <h2 v-if="daerah">{{ daerah }}</h2>

        <FilterSection
            :filters="filters"
            :unique-desa="uniqueDesa"
            :unique-kelompok="uniqueKelompok"
            @filter-change="handleFilterChange"
        />

        <StatisticsSection :stats="filteredStats" />

        <BiodataTable
            :data="filteredData"
            :sort-key="sortKey"
            :sort-order="sortOrder"
            @sort="sortTable"
            @row-click="openDetailModal"
        />

        <BiodataDetailModal
            :show="showModal"
            :data="selectedDetailData"
            :api-key="apiKey"
            @close="closeModal"
        />
    </div>
</template>

<script setup lang="ts">
import * as Vue from "vue";
import type {
    BiodataRecord,
    DetailedBiodataRecord,
    Filters,
    Statistics,
    FilterChangeEvent,
} from "./types/biodata";

import FilterSection from "./components/FilterSection.vue";
import StatisticsSection from "./components/StatisticsSection.vue";
import BiodataTable from "./components/BiodataTable.vue";
import BiodataDetailModal from "./components/BiodataDetailModal.vue";
import "./styles/pantau-biodata.css";

// Reactive data
const biodataData = Vue.ref<BiodataRecord[]>([]);
const detailedData = Vue.ref<Record<number, DetailedBiodataRecord>>({});
const showModal = Vue.ref<boolean>(false);
const selectedDetailData = Vue.ref<DetailedBiodataRecord | null>(null);
const filters = Vue.ref<Filters>({
    sambung_desa: "",
    sambung_kelompok: "",
    nama: "",
});
const apiKey = Vue.ref<string>("");
const daerah = Vue.ref<string>("");
const sortKey = Vue.ref<string>("");
const sortOrder = Vue.ref<"asc" | "desc">("asc");

// Computed properties
const uniqueDesa = Vue.computed<string[]>(() => {
    return [
        ...new Set(
            biodataData.value.map((item: BiodataRecord) => item.sambung_desa),
        ),
    ].sort();
});

const uniqueKelompok = Vue.computed<string[]>(() => {
    let data = biodataData.value;
    if (filters.value.sambung_desa) {
        data = data.filter(
            (item: BiodataRecord) =>
                item.sambung_desa === filters.value.sambung_desa,
        );
    }
    return [
        ...new Set(data.map((item: BiodataRecord) => item.sambung_kelompok)),
    ].sort();
});

const filteredData = Vue.computed<BiodataRecord[]>(() => {
    const filtered = biodataData.value.filter((item: BiodataRecord) => {
        const desaMatch =
            !filters.value.sambung_desa ||
            item.sambung_desa === filters.value.sambung_desa;
        const kelompokMatch =
            !filters.value.sambung_kelompok ||
            item.sambung_kelompok === filters.value.sambung_kelompok;
        const namaMatch =
            !filters.value.nama ||
            item.nama_lengkap
                .toLowerCase()
                .includes(filters.value.nama.toLowerCase()) ||
            item.nama_panggilan
                .toLowerCase()
                .includes(filters.value.nama.toLowerCase());
        return desaMatch && kelompokMatch && namaMatch;
    });

    if (sortKey.value) {
        filtered.sort((a: BiodataRecord, b: BiodataRecord) => {
            let aVal =
                sortKey.value === "index" ? 1 : (a as any)[sortKey.value];
            let bVal =
                sortKey.value === "index" ? 1 : (b as any)[sortKey.value];

            if (typeof aVal === "string") aVal = aVal.toLowerCase();
            if (typeof bVal === "string") bVal = bVal.toLowerCase();

            if (aVal < bVal) return sortOrder.value === "asc" ? -1 : 1;
            if (aVal > bVal) return sortOrder.value === "asc" ? 1 : -1;
            return 0;
        });
    }

    return filtered;
});

const filteredStats = Vue.computed<Statistics>(() => {
    const filtered = filteredData.value;
    let with_photo_count = 0;
    let without_photo_count = 0;

    // Calculate photo stats based on the filtered data
    filtered.forEach((item: BiodataRecord) => {
        const detail = detailedData.value[item.biodata_id];
        if (detail) {
            if (detail.foto_filename) {
                with_photo_count++;
            } else {
                without_photo_count++;
            }
        } else {
            // Assume unknown photo status as "without photo"
            without_photo_count++;
        }
    });

    return {
        total_count: filtered.length,
        male_count: filtered.filter(
            (item: BiodataRecord) =>
                item.jenis_kelamin.toLowerCase() === "laki-laki",
        ).length,
        female_count: filtered.filter(
            (item: BiodataRecord) =>
                item.jenis_kelamin.toLowerCase() === "perempuan",
        ).length,
        with_photo_count: with_photo_count,
        without_photo_count: without_photo_count,
    };
});

// Methods
const handleFilterChange = (newFilters: FilterChangeEvent): void => {
    // If desa is changed, reset kelompok
    if (newFilters.sambung_desa !== filters.value.sambung_desa) {
        newFilters.sambung_kelompok = "";
    }
    filters.value = { ...filters.value, ...newFilters };
};

const fetchBiodataGenerus = async (): Promise<void> => {
    let apiUrl = "/api/biodata/generus/";
    if (daerah.value) {
        apiUrl += `?daerah=${daerah.value}`;
    }

    try {
        const response = await fetch(apiUrl, {
            headers: { Authorization: `ApiKey ${apiKey.value}` },
        });
        if (!response.ok)
            throw new Error(`HTTP error! status: ${response.status}`);
        const data: any = await response.json();
        // Handle both old backend (direct array) and new backend (wrapped in data property)
        biodataData.value = data.data || data;
    } catch (error) {
        console.error("Error fetching data:", error);
    }
};

const fetchDetailedData = async (
    biodataId: number,
): Promise<DetailedBiodataRecord | null> => {
    console.log(
        "🔍 [FETCH DEBUG] Fetching detailed data for biodata ID:",
        biodataId,
    );

    if (detailedData.value[biodataId]) {
        console.log(
            "📋 [FETCH DEBUG] Data already cached, returning cached data",
        );
        return detailedData.value[biodataId]; // Already fetched
    }

    const apiUrl = `/api/biodata/generus/${biodataId}`;
    console.log("🌐 [FETCH DEBUG] API URL:", apiUrl);
    console.log(
        "🔑 [FETCH DEBUG] API Key (first 10 chars):",
        apiKey.value ? apiKey.value.substring(0, 10) + "..." : "NOT PROVIDED",
    );

    try {
        console.log("🚀 [FETCH DEBUG] Making fetch request...");
        const response = await fetch(apiUrl, {
            headers: { Authorization: `ApiKey ${apiKey.value}` },
        });

        console.log(
            "📡 [FETCH DEBUG] Response received, status:",
            response.status,
        );

        if (!response.ok) {
            console.error(
                "❌ [FETCH DEBUG] Response not OK, status:",
                response.status,
            );
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const rawData: any = await response.json();
        console.log("📋 [FETCH DEBUG] Raw data parsed successfully:", rawData);

        // Handle both old backend (direct object) and new backend (wrapped in data property)
        const data = rawData.data || rawData;
        console.log("📋 [FETCH DEBUG] Extracted data:", data);
        console.log(
            "📸 [FETCH DEBUG] Photo filename in response:",
            data.foto_filename,
        );

        // Use direct assignment for Vue 3 reactivity
        detailedData.value[biodataId] = data;
        return data;
    } catch (error) {
        console.error("💥 [FETCH DEBUG] Error fetching detailed data:", error);
        return null;
    }
};

const openDetailModal = async (biodataId: number): Promise<void> => {
    console.log(
        "🔍 [MODAL DEBUG] Opening detail modal for biodata ID:",
        biodataId,
    );
    showModal.value = true;
    selectedDetailData.value = null; // Show loading

    const data = await fetchDetailedData(biodataId);
    if (data) {
        console.log("📋 [MODAL DEBUG] Detail data received:", data);
        console.log("📸 [MODAL DEBUG] Photo filename:", data.foto_filename);
        console.log("🔑 [MODAL DEBUG] API Key available:", !!apiKey.value);
        selectedDetailData.value = data;
    } else {
        console.error(
            "❌ [MODAL DEBUG] No data received for biodata ID:",
            biodataId,
        );
    }
};

const closeModal = (): void => {
    showModal.value = false;
    selectedDetailData.value = null;
};

const sortTable = (key: string): void => {
    if (sortKey.value === key) {
        sortOrder.value = sortOrder.value === "asc" ? "desc" : "asc";
    } else {
        sortKey.value = key;
        sortOrder.value = "asc";
    }
};

// Lifecycle
Vue.onMounted(() => {
    const params = new URLSearchParams(window.location.search);
    apiKey.value = params.get("key") || "";
    daerah.value = params.get("daerah") || "";
    fetchBiodataGenerus();
    document.title = `PANTAU BIODATA GENERUS ${daerah.value}`.trim();
});
</script>

