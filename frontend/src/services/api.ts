import axios from "axios";

// Base URL can be overridden via env; defaults to the provided backend host
const API_BASE = (import.meta as any)?.env?.VITE_API_BASE || "/api";

export const api = axios.create({
  baseURL: API_BASE,
  withCredentials: false,
});

// Attach Authorization header from session storage to avoid tight coupling with Pinia in this module
api.interceptors.request.use((config) => {
  const token = typeof window !== "undefined" ? sessionStorage.getItem("auth_token") : null;
  if (token) {
    config.headers = config.headers || {};
    (config.headers as any)["Authorization"] = `Bearer ${token}`;
  }
  return config;
});

api.interceptors.response.use(
  (resp) => resp,
  (error) => {
    if (error?.response?.status === 401) {
      // Clear token on unauthorized
      if (typeof window !== "undefined") {
        sessionStorage.removeItem("auth_token");
      }
    }
    return Promise.reject(error);
  }
);

export interface LoginResponse {
  access_token: string;
  token_type: string;
}

export interface ApiKey {
  id: string;
  key: string;
  createdAt?: string;
  revoked?: boolean;
}

export interface User {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  is_active: boolean;
  is_staff: boolean;
  is_superuser: boolean;
  date_joined: string;
  last_login?: string;
}

export const login = async (username: string, password: string) => {
  return api.post<LoginResponse>("/auth/login", { username, password });
};

export const getApiKeys = async () => {
  return api.get<ApiKey[]>("/auth/apikeys");
};

export const generateApiKey = async (name: string) => {
  return api.post<ApiKey>("/auth/apikeys", { name });
};

export const revokeApiKey = async (id: string) => {
  return api.delete<void>(`/auth/apikeys/${id}`);
};

// User management endpoints
export const getAllUsers = async () => {
  return api.get<User[]>("/admin/users");
};

export const activateUser = async (id: number) => {
  return api.patch<void>(`/admin/users/${id}/activate`);
};

export const deactivateUser = async (id: number) => {
  return api.patch<void>(`/admin/users/${id}/deactivate`);
};

export const grantAdminPrivileges = async (id: number) => {
  return api.patch<void>(`/admin/users/${id}/grant-admin`);
};

export const revokeAdminPrivileges = async (id: number) => {
  return api.patch<void>(`/admin/users/${id}/revoke-admin`);
};

export const deleteUser = async (id: number) => {
  return api.delete<void>(`/admin/users/${id}`);
};

