/* Global styles to restore pre-TypeScript UI look-and-feel */

/* Color system and shared tokens */
:root {
  --color-primary: #2e5a35;
  --color-primary-600: #3d7a47;
  --color-text: #2c4a3e;
  --color-muted: #6c757d;
  --color-bg: #ffffff;

  --radius-lg: 20px;
  --radius-md: 12px;
  --radius-sm: 8px;

  --shadow-1: 0 10px 25px rgba(44, 74, 62, 0.2), 0 6px 12px rgba(44, 74, 62, 0.15);
  --shadow-2: 0 2px 6px rgba(0, 0, 0, 0.1);
}

/* Base reset */
*,
*::before,
*::after {
  box-sizing: border-box;
}

html,
body {
  margin: 0;
  padding: 0;
  background: var(--color-bg);
  color: var(--color-text);
  font-family:
    -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu,
    Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography helpers */
h1 {
  font-size: 2.5em;
  text-align: center;
  margin-bottom: 20px;
}

.hidden {
  opacity: 0;
  pointer-events: none;
  position: absolute;
  left: -9999px;
  visibility: hidden;
  display: none;
  z-index: -1;
}

/* Container restored from legacy .form-container */
.form-container {
  width: 500px;
  border-radius: var(--radius-lg);
  background-color: #f9f9f9;
  box-shadow: var(--shadow-1);
  text-align: center;
  margin: 10px auto;
  padding: 20px;
}

/* Section and form titles */
.form-title {
  font-size: 22px;
  font-weight: bold;
  color: var(--color-primary);
  margin-bottom: 10px;
}

.form-subtitle {
  font-size: 20px;
  color: var(--color-primary);
  font-weight: bold;
  margin-bottom: 20px;
}

.section-title {
  font-size: 21px;
  font-weight: bold;
  color: var(--color-primary);
  margin: 30px 20% 25px 20%;
  padding-bottom: 10px;
  border-bottom: 2px solid rgba(46, 90, 53, 0.3);
  text-align: center;
  align-items: center;
}

/* Generic form layout and fields */
form {
  text-align: left;
}

.form-group {
  margin-bottom: 15px;
}

label {
  display: block;
  margin-bottom: 8px;
  color: var(--color-text);
  font-weight: 650;
  font-size: 16px;
  text-align: left;
  padding-top: 10px;
}

input,
select,
textarea {
  width: 90%;
  padding: 10px;
  margin-bottom: 15px;
  margin-left: 8%;
  margin-right: 5%;
  border: none;
  border-bottom: 2px solid var(--color-primary);
  border-radius: var(--radius-sm);
  font-size: 16px;
  transition: all 0.3s ease;
  box-sizing: border-box;
  appearance: none;
  background-color: rgba(46, 90, 53, 0.001);
  color: var(--color-text);
}

textarea {
  height: 100px;
  resize: vertical;
  border-left: none;
  border-right: none;
  border-top: none;
  border-bottom: 2px solid var(--color-primary);
  border-radius: var(--radius-sm);
}

input::placeholder,
textarea::placeholder {
  color: var(--color-primary);
  opacity: 0.7;
}

input:focus,
select:focus,
textarea:focus {
  outline: none;
  border-bottom: 2px solid var(--color-primary-600);
  box-shadow: none;
  background-color: rgba(46, 90, 53, 0.05);
}

/* Nice select arrow */
select {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%232e5a35' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 10px center;
  background-size: 16px;
  padding-right: 40px;
}

/* Buttons */
button {
  border: none;
  border-radius: var(--radius-lg);
  background-color: var(--color-primary);
  color: #fff;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.2s ease, opacity 0.2s ease;
  box-sizing: border-box;
  margin-top: 20px;
  font-weight: 600;
  height: 50px;
  padding: 0 20px;
}

button:hover {
  background-color: var(--color-primary-600);
}

button.secondary {
  background-color: #ffffff;
  color: var(--color-primary);
  border: 2px solid var(--color-primary);
  margin-top: 10px;
}

button.secondary:hover {
  background-color: #f0f0f0;
}

/* Review section */
.review-data {
  text-align: left;
  padding: 10px;
  background-color: #ffffff;
  border-radius: 15px;
  margin: 20px 0;
}

.review-item {
  padding: 10px 15px;
  border-bottom: 1px solid rgba(46, 90, 53, 0.1);
  font-size: 16px;
  line-height: 1.5;
}

.review-item:last-child {
  border-bottom: none;
}

.review-actions {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

/* Suggestions dropdowns (Kelompok, Sekolah/Kelas, Hobi) */
.suggestions-container {
  position: relative;
  flex: 1;
  width: 100%;
}

.suggestions {
  position: absolute;
  width: 100%;
  max-height: 200px;
  overflow-y: auto;
  background: white;
  border: 1px solid var(--color-primary);
  border-radius: 10px;
  z-index: 1000;
  margin-top: -10px;
  box-shadow: var(--shadow-2);
  text-align: left;
}

.suggestion-item {
  padding: 10px 15px;
  cursor: pointer;
  border-bottom: 1px solid rgba(46, 90, 53, 0.1);
}

.suggestion-item:hover {
  background-color: rgba(46, 90, 53, 0.1);
}

/* Loading and error states inline with suggestions components */
.loading-indicator {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #ccc;
  border-top: none;
  z-index: 1000;
  padding: 8px;
  text-align: center;
  border-radius: 0 0 8px 8px;
}

.error-message {
  color: #ff4444;
  font-size: 14px;
  margin-top: 10px;
  padding: 8px;
  background: #ffe6e6;
  border-radius: 4px;
  border: 1px solid #ffcccc;
}

/* Hobi tags */
.selected-hobi-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 10px;
}

.hobi-tag {
  background-color: #e8f5e9;
  border: 1px solid var(--color-primary);
  border-radius: 16px;
  padding: 6px 12px;
  font-size: 14px;
  color: var(--color-text);
  display: flex;
  align-items: center;
}

.remove-hobi {
  margin-left: 6px;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  color: var(--color-primary);
  line-height: 1;
}

.remove-hobi:hover {
  color: #d32f2f;
}

/* Spacing divider used in form */
.spaci {
  margin: 20px 0 30px 0;
  padding: 0 5% 0 5%;
  border-bottom: 2px solid rgba(46, 90, 53, 0.1);
}

/* Success confirmation */
.confirmation-container {
  text-align: center;
}

.confirmation-message {
  font-size: 20px;
  font-weight: bold;
  color: var(--color-primary);
  margin: 20px 0;
  line-height: 1.5;
}

/* Photo review thumbnail (fallback if component-scoped not applied) */
.review-photo {
  max-width: 150px;
  max-height: 150px;
  border-radius: 8px;
  border: 2px solid #ddd;
  object-fit: cover;
}

/* Responsive */
@media (max-width: 520px) {
  .form-container {
    width: 95%;
    padding: 15px;
  }

  .section-title {
    margin: 20px 10% 20px 10%;
  }

  input,
  select,
  textarea {
    width: 100%;
    margin-left: 0;
    margin-right: 0;
  }
}
