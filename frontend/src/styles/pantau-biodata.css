body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
    background-color: #f8f9fa;
    color: #333;
}

.pantau-biodata-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

h1 {
    font-size: 2.5em;
    text-align: center;
    margin-bottom: 10px;
    color: #2e5a35;
}

h2 {
    font-size: 1.8em;
    text-align: center;
    margin-bottom: 20px;
    color: #495057;
}

.filter-section {
    background-color: #fff;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    margin-bottom: 25px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    align-items: flex-end;
}

.filter-item {
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 100;
}

.filter-item label {
    margin-bottom: 8px;
    font-weight: bold;
    color: #495057;
}

.filter-item select,
.filter-item input {
    border: 1px solid #ced4da;
    padding: 12px;
    font-size: 16px;
    background-color: #fff;
    border-radius: 10px;
    width: 100%;
    box-sizing: border-box;
    transition: border-color 0.2s, box-shadow 0.2s;
}

.filter-item select:focus,
.filter-item input:focus {
    outline: none;
    border-color: #2e5a35;
    box-shadow: 0 0 0 3px rgba(46, 90, 53, 0.15);
}

.table-container {
    width: 100%;
    background-color: #fff;
    border-radius: 15px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

table {
    width: 100%;
    border-collapse: collapse;
}

th,
td {
    padding: 16px 20px;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
    white-space: nowrap;
}

th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #343a40;
    cursor: pointer;
    user-select: none;
    transition: background-color 0.2s;
}

th:hover {
    background-color: #e9ecef;
}

th span {
    margin-left: 8px;
    color: #2e5a35;
}

tr:last-child td {
    border-bottom: none;
}

.clickable-row {
    cursor: pointer;
    transition: background-color 0.2s;
}

.clickable-row:hover {
    background-color: #f1f3f5;
}

@media (max-width: 768px) and (min-width: 521px) {
    .filter-section {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 520px) {
    .filter-section {
        grid-template-columns: 1fr;
    }
}