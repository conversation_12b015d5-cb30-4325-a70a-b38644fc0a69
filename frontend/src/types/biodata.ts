// Form Data Interfaces
export interface FormData {
  nama_lengkap: string;
  nama_panggilan: string;
  jenis_kelamin: "LAKI-LAKI" | "PEREMPUAN" | "";
  kelahiran_tempat: string;
  kelahiran_tanggal: string;
  sambung_desa: string;
  sambung_kelompok: string;
  alamat_tinggal: string;
  nomor_hape?: string;
  sekolah_kelas?: string;
  sekolah?: string;
  kelas?: string;
  nama_ayah: string;
  status_ayah: "SUDAH NGAJI" | "BELUM NGAJI" | "";
  nomor_hape_ayah: string;
  nama_ibu: string;
  status_ibu: "SUDAH NGAJI" | "BELUM NGAJI" | "";
  nomor_hape_ibu: string;
  catatan?: string;
  pendataan_tanggal: string;
  [key: string]: any;
}

// Hobby Interfaces
// Removed duplicate HobiItem interface

export interface HobiCategory {
  [category: string]: string[];
}

export interface HobiItem {
  id: number;
  kategori: string;
  hobi: string;
  detail_hobi?: string;
}

export interface HobiData {
  options: HobiItem[];
  isLoading: boolean;
  dataLoaded: boolean;
  loadError: string | null;
}

// Kelompok/Group Interfaces
export interface KelompokOptions {
  [desa: string]: string[];
}

export interface KelompokData {
  options: KelompokOptions;
  isLoading: boolean;
  dataLoaded: boolean;
  loadError: string | null;
}

export interface FlattenedKelompok {
  desa: string;
  kelompok: string;
}

// School/Class Interfaces
export interface SekolahKelasOption {
  jenjang: string;
  kelas: string;
}

// Biodata Record (for monitoring component)
export interface BiodataRecord {
  biodata_id: number;
  nama_lengkap: string;
  nama_panggilan: string;
  jenis_kelamin: string;
  kelahiran_tempat: string;
  kelahiran_tanggal: string;
  sambung_desa: string;
  sambung_kelompok: string;
  sekolah: string;
  kelas: string;
  created_at: string;
  updated_at: string;
}

export interface DetailedBiodataRecord extends BiodataRecord {
  alamat_tinggal?: string;
  nomor_hape?: string;
  nama_ayah?: string;
  status_ayah?: string;
  nomor_hape_ayah?: string;
  nama_ibu?: string;
  status_ibu?: string;
  nomor_hape_ibu?: string;
  hobi?: HobiCategory;
  foto_filename?: string;
  catatan?: string;
}

// Filter Interfaces
export interface Filters {
  sambung_desa: string;
  sambung_kelompok: string;
  nama: string;
}

// Statistics Interface
export interface Statistics {
  total_count: number;
  male_count: number;
  female_count: number;
  with_photo_count: number;
  without_photo_count: number;
}

// API Response Interfaces
export interface ApiResponse<T> {
  data: T;
  success?: boolean;
  message?: string;
}

// Component State Interfaces
export interface DataState<T> {
  options: T;
  isLoading: boolean;
  dataLoaded: boolean;
  loadError: string | null;
}

// Event Interfaces
export interface FilterChangeEvent {
  sambung_desa?: string;
  sambung_kelompok?: string;
  nama?: string;
}

export interface EditFormData {
  savedAddress?: string;
}
