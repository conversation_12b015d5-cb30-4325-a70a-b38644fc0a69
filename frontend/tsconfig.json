{"compilerOptions": {"target": "ES2022", "lib": ["ES2022", "DOM", "DOM.Iterable"], "module": "ESNext", "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "checkJs": false, "declaration": false, "declarationMap": false, "sourceMap": false, "outDir": "./dist", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "noImplicitReturns": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true, "jsx": "preserve", "isolatedModules": true, "types": ["@cloudflare/workers-types", "node", "vite/client", "vue"], "baseUrl": ".", "paths": {"@/*": ["src/*"]}}, "include": ["server/**/*", "src/**/*", "src/**/*.ts", "src/**/*.vue", "src/types/**/*.d.ts"], "exclude": ["node_modules", "dist", "**/*.test.*", "src/oldjs/**/*"]}