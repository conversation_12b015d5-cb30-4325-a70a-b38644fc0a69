# Deployment Scripts

This directory contains deployment scripts for both the Python FastAPI backend and the new Go backend.

## Scripts Overview

### Core Deployment Scripts

- **`faster`** - Original Python FastAPI + Django backend deployment
- **`go-deploy`** - New Go backend deployment (containerized)
- **`migrate-to-go`** - Migration script from Python to Go backend
- **`test-go-deployment`** - Test script for Go backend deployment

## Quick Start

### Deploy Go Backend

```bash
# Initialize and start Go backend
./script/go-deploy init
./script/go-deploy start

# Test deployment
./script/test-go-deployment besb
```

### Migrate from Python to Go

```bash
# Automated migration with backup and rollback
./script/migrate-to-go besb
```

## Deployment Comparison

| Feature | Python Backend (`faster`) | Go Backend (`go-deploy`) |
|---------|---------------------------|--------------------------|
| **Language** | Python (FastAPI + Django) | Go (Gin framework) |
| **Memory Usage** | ~200MB | ~50MB |
| **Startup Time** | ~5 seconds | ~1 second |
| **Container Count** | 7 containers | 6 containers |
| **Database** | PostgreSQL (shared) | PostgreSQL (same DB) |
| **Caching** | Redis | Redis |
| **Reverse Proxy** | Nginx | Nginx |
| **API Compatibility** | Original | 100% compatible |

## Container Architecture

### Python Backend Containers
```
faster_pod/
├── besb_postgres     (PostgreSQL database)
├── besb_redis        (Redis cache)
├── besb_django       (Django auth service)
├── besb_uvicorn      (FastAPI application)
├── besb_nginx        (Reverse proxy)
├── besb_cfltunnel    (Cloudflare tunnel)
└── besb_pgadmin      (Database admin)
```

### Go Backend Containers
```
go_pod/
├── besb_postgres     (PostgreSQL database - same as Python)
├── besb_redis        (Redis cache)
├── besb_go_backend   (Go application)
├── besb_nginx        (Reverse proxy)
├── besb_cfltunnel    (Cloudflare tunnel)
└── besb_pgadmin      (Database admin)
```

## Command Reference

### go-deploy Commands

```bash
./script/go-deploy <app_name> <command>
```

#### Core Operations
- `init` - Initialize project structure
- `start` - Start all services
- `stop` - Stop all services
- `restart` - Restart all services
- `status` - Show container status

#### Development
- `rebuild` - Rebuild Go image and restart
- `logs` - Show container logs
- `shell` - Open container shell
- `test_api` - Test API endpoints

#### Maintenance
- `cek` - Check and restart failed containers
- `backup_db` - Create database backup
- `pg` - Start pgAdmin

### faster Commands

```bash
./script/faster <app_name> <command>
```

#### Core Operations
- `init` - Initialize Python project
- `start` - Start all services
- `stop` - Stop all services
- `db` - Run database migrations
- `cek` - Check container health

## Configuration

### Environment Variables

Both deployments use compatible environment variables:

```bash
# Database (shared between Python and Go)
POSTGRES_USER=besb_user
POSTGRES_PASSWORD=j1GjXwedxGBuUAkLxAN6BROcCi3oWn7G
POSTGRES_DB=besb_db

# Authentication (shared JWT secret)
JWT_SECRET=Pxf0AsnFeejnpZfp4Ya8F4wsyJcqSV2Q

# CORS (same allowed origins)
CORS_ALLOWED_ORIGINS="https://ngaji.brkh.work,https://29.brkh.work,https://hj.brkh.work,https://hd.brkh.work"
```

### Ports

| Service | Port | Description |
|---------|------|-------------|
| Nginx | 8080 | External access (both deployments) |
| Backend | 8000 | Application server |
| Django | 8001 | Django auth (Python only) |
| PostgreSQL | 5432 | Database (internal) |
| Redis | 6379 | Cache (internal) |
| pgAdmin | 5050 | Database admin |

### Volumes

#### Python Backend
- `~/projects/api_besb/support/db_data` - Database
- `~/projects/api_besb/support/redis_data` - Redis
- `~/projects/api_besb/main/fotos` - Photos

#### Go Backend
- `~/projects/go_besb/support/db_data` - Database
- `~/projects/go_besb/support/redis_data` - Redis
- `~/projects/go_besb/support/fotos` - Photos

## Migration Process

### Automated Migration

```bash
./script/migrate-to-go besb
```

This script:
1. ✅ Backs up current state
2. ✅ Stops Python backend
3. ✅ Migrates photo files
4. ✅ Starts Go backend
5. ✅ Tests deployment
6. ✅ Provides rollback if needed

### Manual Migration

```bash
# 1. Backup current state
./script/faster besb run_cmd "pg_dump -U besb_user besb_db > backup.sql"

# 2. Stop Python backend
./script/faster besb stop

# 3. Initialize Go backend
./script/go-deploy init

# 4. Start Go backend
./script/go-deploy start

# 5. Test deployment
./script/test-go-deployment besb
```

### Rollback

```bash
# Stop Go backend
./script/go-deploy stop

# Start Python backend
./script/faster besb start
```

## Monitoring

### Health Checks

```bash
# Go backend health
curl http://localhost:8080/health

# Container status
./script/go-deploy status
```

### Logs

```bash
# Go backend logs
./script/go-deploy logs

# All container logs
podman pod logs besb_go_pod
```

### Performance Monitoring

```bash
# Resource usage
podman stats

# API performance test
./script/test-go-deployment besb
```

## Troubleshooting

### Common Issues

1. **Port Conflicts**
   ```bash
   # Check what's using port 8080
   ss -tlnp | grep 8080

   # Stop conflicting service
   ./script/faster besb stop
   ```

2. **Database Connection**
   ```bash
   # Check PostgreSQL
   ./script/go-deploy shell
   # Inside container:
   wget -qO- http://localhost:8000/health
   ```

3. **Build Failures**
   ```bash
   # Check Go source
   ls -la /home/<USER>/gnrs/new-backend/

   # Rebuild
   ./script/go-deploy rebuild
   ```

### Debug Mode

```bash
# Enable verbose logging
./script/go-deploy logs

# Interactive debugging
./script/go-deploy shell
```

## Best Practices

### Development
- Use `rebuild` for code changes
- Monitor logs during development
- Test API endpoints after changes

### Production
- Regular database backups
- Monitor container health
- Use `cek` command for auto-restart
- Keep Cloudflare tunnel token secure

### Security
- Secrets stored with 600 permissions
- Containers run as non-root users
- Network isolation within pods
- Regular security updates

## Support

For issues:
1. Check container status: `./script/go-deploy status`
2. Review logs: `./script/go-deploy logs`
3. Test API: `./script/test-go-deployment besb`
4. Use rollback if needed: `./script/migrate-to-go besb` (rollback option)
