#!/bin/bash
# Go Backend Deployment Script - <PERSON><PERSON> containerized deployment
# Usage: ./be <command>

# Function to generate a random password
generate_password() {
  </dev/urandom tr -dc 'A-Za-z0-9' | head -c 32
}

# Check if command argument is provided
if [ -z "$1" ]; then
  echo "Error: App name is required"
  echo "Usage: $0 <command>"
  exit 1
fi

APP_NAME="GNRS"
COMMAND="$1"

# Set up directory paths
PROJECT_DIR="$HOME/gnrs/backend"
SUPPORT_DIR="${PROJECT_DIR}/support"
GO_BACKEND_DIR="$PROJECT_DIR/src"
ROOT_DIR="$PROJECT_DIR/.root_dir"

# Container names
POD_NAME="${APP_NAME}_go_pod"
POSTGRES_CONTAINER_NAME="${APP_NAME}_postgres"
REDIS_CONTAINER_NAME="${APP_NAME}_redis"
GO_BACKEND_CONTAINER_NAME="${APP_NAME}_go_backend"
NGINX_CONTAINER_NAME="${APP_NAME}_nginx"
PGADMIN_CONTAINER_NAME="${APP_NAME}_pgadmin"
CFL_TUNNEL_CONTAINER_NAME="${APP_NAME}_cfltunnel"
INTERACT_CONTAINER_NAME="${APP_NAME}_interact"

# Load environment configuration
ENV_FILE="${SUPPORT_DIR}/env.conf"
if [ ! -f "$ENV_FILE" ]; then
  echo "Creating environment configuration..."
  mkdir -p "$SUPPORT_DIR"
  cat >"$ENV_FILE" <<EOL
# Application settings
HOST_DOMAIN="pkkwjv-gnrs.var.my.id"
PORT1="8080"  # Nginx port
PORT2="8000"  # Go backend port

# Container images
POSTGRES_IMAGE="docker.io/library/postgres:16-alpine"
GO_IMAGE="docker.io/library/golang:tip-alpine"
REDIS_IMAGE="docker.io/library/redis:latest"
NGINX_IMAGE="docker.io/library/nginx:latest"
PGADMIN_IMAGE="docker.io/dpage/pgadmin4:latest"
INTERACT_IMAGE="docker.io/library/golang:tip-alpine"

# Path settings
ROOT_DIR="$ROOT_DIR"
GO_BACKEND_DIR="$GO_BACKEND_DIR"
EOL
fi

# Generate secrets if not exists (reuse existing secrets for compatibility)
if [ ! -f "${SUPPORT_DIR}/secrets.env" ]; then
  # Use same credentials as Python backend for database compatibility
  cat >"${SUPPORT_DIR}/secrets.env" <<SECRETS
# Database credentials (compatible with existing Python backend)
POSTGRES_USER=gnrs_user
POSTGRES_PASSWORD=$(generate_password)
POSTGRES_DB=gnrs_db

# JWT settings (compatible with existing Python backend)
JWT_SECRET=$(generate_password)
JWT_ACCESS_TOKEN_TTL=900
JWT_REFRESH_TOKEN_TTL=4500

# Redis settings
REDIS_PASSWORD=$(generate_password)

# pgAdmin settings
PGADMIN_DEFAULT_PASSWORD=$(generate_password)

# CORS settings (compatible with existing frontend)
CORS_ALLOWED_ORIGINS="https://ngaji.brkh.work,https://29.brkh.work,https://hj.brkh.work,https://hd.brkh.work"

# File upload settings
MAX_FILE_SIZE=2097152
FOTOS_DIR=fotos
SECRETS
  chmod 600 "${SUPPORT_DIR}/secrets.env"
fi

# Source environment configuration
if [ -f "$ENV_FILE" ]; then
  # shellcheck source=/dev/null
  source "$ENV_FILE"
else
  echo "Error: Environment configuration file not found: $ENV_FILE"
  exit 1
fi

# Load secrets
set -a
if [ -f "${SUPPORT_DIR}/secrets.env" ]; then
  # shellcheck source=/dev/null
  source "${SUPPORT_DIR}/secrets.env"
else
  echo "Warning: secrets.env not found, skipping"
fi
set +a

# Cleanup function to unset environment variables
unset_secrets() {
  unset POSTGRES_USER POSTGRES_PASSWORD POSTGRES_DB JWT_SECRET REDIS_PASSWORD
}
trap unset_secrets EXIT

# Check if podman is installed
if ! command -v podman &>/dev/null; then
  echo "Error: podman is not installed"
  exit 1
fi

init() {
  # Check if project directory already exists
  if [ -d "$PROJECT_DIR" ]; then
    echo "Warning: Project directory already exists. Checking for existing files..."
  fi

  echo "Creating project directories..."
  # Create all required directories
  for dir in \
    "$PROJECT_DIR" \
    "$SUPPORT_DIR" \
    "$ROOT_DIR" \
    "$SUPPORT_DIR/db_data" \
    "$SUPPORT_DIR/redis_data" \
    "$SUPPORT_DIR/pgadmin" \
    "$SUPPORT_DIR/logs" \
    "$SUPPORT_DIR/fotos"; do
    if [ ! -d "$dir" ]; then
      mkdir -p "$dir"
    else
      echo "  Directory already exists: $dir"
    fi
  done

  # Create token file if it doesn't exist
  if [ ! -f "$SUPPORT_DIR/token" ]; then
    touch "$SUPPORT_DIR/token"
    echo "  Created token file: $SUPPORT_DIR/token"
  else
    echo "  Token file already exists: $SUPPORT_DIR/token"
  fi

  # Set permissions for pgadmin directory
  chmod 777 "$SUPPORT_DIR/pgadmin"

  # Only create .gitignore if it doesn't exist
  if [ ! -f "$PROJECT_DIR/.gitignore" ]; then
    echo "Creating .gitignore..."
    cat >"$PROJECT_DIR/.gitignore" <<EOL
# Project specific
support/db_data/
support/redis_data/
support/pgadmin/
support/token
support/logs/
support/*.log
support/secrets.env
support/backups/
support/env.conf
support/fotos/

# Go
*.exe
*.exe~
*.dll
*.so
*.dylib
*.test
*.out
go.work

# IDE
.idea/
.vscode/
*.swp
*.swo
*~

# Logs
*.log
log/
logs/

# Database
*.sqlite3
*.db

# System Files
.DS_Store
Thumbs.db
EOL
    echo "  Created .gitignore file"
  else
    echo "  .gitignore file already exists: $PROJECT_DIR/.gitignore"
  fi

  echo "Go backend project initialized successfully!"
}

stop() {
  echo "Stopping and removing pod..."
  podman pod stop "$POD_NAME" || true
  podman pod rm "$POD_NAME" || true
}

build() {
  echo "Building Go backend Docker image..."
  if [ ! -d "$GO_BACKEND_DIR" ]; then
    echo "Error: Go backend directory not found: $GO_BACKEND_DIR"
    return 1
  fi

  # Build the Go backend image
  podman build -t "gnrs-go:latest" "$GO_BACKEND_DIR" || {
    echo "Failed to build Go backend image"
    return 1
  }
  echo "Go backend image built successfully"
}

run_postgres() {
  echo "Starting PostgreSQL container..."
  podman run -d --pod "$POD_NAME" --name "$POSTGRES_CONTAINER_NAME" \
    -e POSTGRES_DB="$POSTGRES_DB" \
    -e POSTGRES_USER="$POSTGRES_USER" \
    -e POSTGRES_PASSWORD="$POSTGRES_PASSWORD" \
    -v "$SUPPORT_DIR/db_data:/var/lib/postgresql/data:z" \
    "$POSTGRES_IMAGE"
}

wait_for_postgres() {
  echo "Waiting for PostgreSQL to be ready..."
  for i in {1..30}; do
    if podman exec -it "$POSTGRES_CONTAINER_NAME" pg_isready -U "$POSTGRES_USER" &>/dev/null; then
      echo "PostgreSQL is ready."
      return 0
    fi
    echo "Waiting for PostgreSQL... ($i/30)"
    sleep 2
  done
  echo "PostgreSQL did not become ready in time."
  return 1
}

run_redis() {
  echo "Starting Redis container..."
  podman run -d --pod "$POD_NAME" --name "$REDIS_CONTAINER_NAME" \
    -v "$SUPPORT_DIR/redis_data:/data:z" \
    "$REDIS_IMAGE" redis-server --loglevel warning
}

run_go_backend() {
  echo "Starting Go backend container..."
  podman run -d --pod "$POD_NAME" --name "$GO_BACKEND_CONTAINER_NAME" \
    -v "$SUPPORT_DIR/fotos:/app/fotos:z" \
    -e "PORT=8000" \
    -e "ENVIRONMENT=production" \
    -e "POSTGRES_CONTAINER_NAME=$POSTGRES_CONTAINER_NAME" \
    -e "POSTGRES_USER=$POSTGRES_USER" \
    -e "POSTGRES_PASSWORD=$POSTGRES_PASSWORD" \
    -e "POSTGRES_DB=$POSTGRES_DB" \
    -e "POSTGRES_PORT=5432" \
    -e "REDIS_CONTAINER_NAME=$REDIS_CONTAINER_NAME" \
    -e "REDIS_PORT=6379" \
    -e "JWT_SECRET=$JWT_SECRET" \
    -e "JWT_ACCESS_TOKEN_TTL=$JWT_ACCESS_TOKEN_TTL" \
    -e "JWT_REFRESH_TOKEN_TTL=$JWT_REFRESH_TOKEN_TTL" \
    -e "CORS_ALLOWED_ORIGINS=$CORS_ALLOWED_ORIGINS" \
    -e "MAX_FILE_SIZE=$MAX_FILE_SIZE" \
    -e "FOTOS_DIR=$FOTOS_DIR" \
    "gnrs-go:latest"
}

wait_for_go_backend() {
  echo "Waiting for Go backend to be ready..."
  for i in {1..60}; do
    if podman exec -it "$GO_BACKEND_CONTAINER_NAME" wget --no-verbose --tries=1 --spider http://localhost:8000/health &>/dev/null; then
      echo "Go backend is ready."
      return 0
    fi
    echo "Waiting for Go backend... ($i/60)"
    sleep 2
  done
  echo "Go backend did not become ready in time."
  return 1
}

run_nginx() {
  echo "Creating nginx configuration for Go backend..."
  cat >"$SUPPORT_DIR/nginx.conf" <<EOL
server {
    listen 8080;
    server_name 127.0.0.1;

    # Proxy all requests to Go backend
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;

        # Handle large file uploads
        client_max_body_size 2M;

        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}
EOL

  echo "Starting Nginx container..."
  podman run -d --pod "$POD_NAME" --name "$NGINX_CONTAINER_NAME" \
    -v "$SUPPORT_DIR/nginx.conf:/etc/nginx/conf.d/default.conf:ro" \
    "$NGINX_IMAGE"
}

run_cfl_tunnel() {
  if [ ! -s "$SUPPORT_DIR/token" ]; then
    echo "Error: Cloudflare tunnel token is empty. Please add your token to $SUPPORT_DIR/token"
    return 1
  fi

  echo "Starting Cloudflare tunnel..."
  podman run -d --pod "$POD_NAME" --name "$CFL_TUNNEL_CONTAINER_NAME" \
    docker.io/cloudflare/cloudflared:latest tunnel --no-autoupdate run \
    --token "$(cat "$SUPPORT_DIR/token")"
}

run_interact() {
  echo "Starting interactive container..."
  podman run -d --pod "$POD_NAME" --name "$INTERACT_CONTAINER_NAME" \
    -v "$ROOT_DIR:/root:z" \
    -v "$GO_BACKEND_DIR:/app:z" \
    -e "POSTGRES_CONTAINER_NAME=$POSTGRES_CONTAINER_NAME" \
    -e "REDIS_CONTAINER_NAME=$REDIS_CONTAINER_NAME" \
    -e "POSTGRES_USER=$POSTGRES_USER" \
    -e "POSTGRES_PASSWORD=$POSTGRES_PASSWORD" \
    -e "POSTGRES_DB=$POSTGRES_DB" \
    -w /app \
    "$INTERACT_IMAGE" sh -c "sleep infinity"
}

run_cmd() {
  podman exec -it "$INTERACT_CONTAINER_NAME" \
    sh -c "$*"
}

pg() {
  echo "Starting pgAdmin container..."
  podman run -d --pod "$POD_NAME" --name "$PGADMIN_CONTAINER_NAME" \
    -e "PGADMIN_DEFAULT_EMAIL=<EMAIL>" \
    -e "PGADMIN_DEFAULT_PASSWORD=SuperSecret" \
    -e "PGADMIN_LISTEN_PORT=5050" \
    -v "$SUPPORT_DIR/pgadmin:/var/lib/pgadmin:z" \
    "$PGADMIN_IMAGE"
}

pod_create() {
  echo "Creating pod..."
  podman pod create --name "$POD_NAME" --restart always --network bridge
}

esse() {
  # build || return 1
  run_postgres || return 1
  wait_for_postgres
  run_redis || return 1
  run_go_backend || return 1
  wait_for_go_backend
  run_nginx || return 1
  run_cfl_tunnel || return 1
  run_interact || return 1
}

start() {
  pod_create
  esse
  pg
}

cek() {
  if podman pod exists "$POD_NAME"; then
    if [ "$(podman pod ps --filter name="$POD_NAME" --format "{{.Status}}" | awk '{print $1}')" = "Running" ]; then
      for container in "${POSTGRES_CONTAINER_NAME}" "${REDIS_CONTAINER_NAME}" "${GO_BACKEND_CONTAINER_NAME}" "${NGINX_CONTAINER_NAME}" "${CFL_TUNNEL_CONTAINER_NAME}" "${INTERACT_CONTAINER_NAME}" "${PGADMIN_CONTAINER_NAME}"; do
        if [ "$(podman ps --filter name="$container" --format "{{.Status}}" | awk '{print $1}')" != "Up" ]; then
          echo "Container $container is not running. Restarting..."
          podman start "$container" || {
            echo "Failed to restart $container"
            return 1
          }
        fi
      done
      echo "All containers are running."
    else
      echo "Pod is not running. Starting pod..."
      podman pod start "$POD_NAME" || {
        echo "Failed to start pod"
        return 1
      }
    fi
  else
    echo "Pod does not exist. Creating and starting..."
    start
  fi
}

logs() {
  container_name="${3:-$GO_BACKEND_CONTAINER_NAME}"
  echo "Showing logs for $container_name..."
  podman logs -f "$container_name"
}

status() {
  echo "=== Pod Status ==="
  if podman pod exists "$POD_NAME"; then
    podman pod ps --filter name="$POD_NAME"
    echo ""
    echo "=== Container Status ==="
    podman ps --filter pod="$POD_NAME" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
  else
    echo "Pod $POD_NAME does not exist"
  fi
}

restart() {
  echo "Restarting Go backend deployment..."
  stop
  sleep 5
  start
}

rebuild() {
  echo "Rebuilding and restarting Go backend..."
  # Stop the Go backend container
  podman stop "$GO_BACKEND_CONTAINER_NAME" || true
  podman rm "$GO_BACKEND_CONTAINER_NAME" || true

  # Rebuild the image
  build || return 1

  # Restart the Go backend container
  run_go_backend || return 1
  wait_for_go_backend

  echo "Go backend rebuilt and restarted successfully"
}

shell() {
  container_name="${3:-$GO_BACKEND_CONTAINER_NAME}"
  echo "Opening shell in $container_name..."
  podman exec -it "$container_name" sh
}

test_api() {
  echo "Testing Go backend API endpoints..."

  # Test health endpoint
  echo "1. Testing health endpoint..."
  if podman exec -it "$GO_BACKEND_CONTAINER_NAME" wget -qO- http://localhost:8000/health; then
    echo "✓ Health endpoint working"
  else
    echo "✗ Health endpoint failed"
  fi

  # Test root endpoint
  echo "2. Testing root endpoint..."
  if podman exec -it "$GO_BACKEND_CONTAINER_NAME" wget -qO- http://localhost:8000/; then
    echo "✓ Root endpoint working"
  else
    echo "✗ Root endpoint failed"
  fi

  echo "API test completed"
}

register_user() {
  # Usage: be register_user <username> <password> [role]
  local username="$1"
  local password="$2"
  local role="${3:-user}"

  if [ -z "$username" ] || [ -z "$password" ]; then
    echo "Usage: $0 register_user <username> <password> [role]"
    echo "  role: 'user' (default) or 'admin'"
    return 1
  fi

  # Ensure interactive container is running
  if ! podman ps --filter name="${INTERACT_CONTAINER_NAME}" --format '{{.Names}}' | grep -q "."; then
    echo "Error: interactive container '${INTERACT_CONTAINER_NAME}' is not running."
    echo "Start the stack with: $0 start"
    return 1
  fi

  local url="http://127.0.0.1:8080/auth/register"
  echo "Registering user '$username' (role: $role) via $url ..."

  # Use the backend container directly with curl (already installed)
  podman exec "${GO_BACKEND_CONTAINER_NAME}" curl -sS -X POST \
    -H "Content-Type: application/json" \
    -d "{\"username\":\"$username\",\"password\":\"$password\",\"role\":\"$role\"}" \
    "http://localhost:8000/auth/register"
}

bootstrap_admin() {
  # Usage: be bootstrap_admin <username> <password>
  local username="$1"
  local password="$2"

  if [ -z "$username" ] || [ -z "$password" ]; then
    echo "Usage: $0 bootstrap_admin <username> <password>"
    echo "Creates the first super admin user"
    return 1
  fi

  echo "Creating bootstrap admin user '$username'..."
  register_user "$username" "$password" "admin"
}

activate_user() {
  # Usage: be activate_user <username>
  local username="$1"

  if [ -z "$username" ]; then
    echo "Usage: $0 activate_user <username>"
    return 1
  fi

  # Get admin token
  local token
  token=$(get_admin_token)
  if [ -z "$token" ]; then
    echo "Error: Cannot get admin token for CLI operations"
    return 1
  fi

  # Get user ID first
  local user_id
  user_id=$(get_user_id "$username")
  if [ -z "$user_id" ]; then
    echo "Error: User '$username' not found"
    return 1
  fi

  echo "Activating user '$username' (ID: $user_id)..."
  local result
  result=$(podman exec "${GO_BACKEND_CONTAINER_NAME}" curl -sS -X PATCH \
    -H "Authorization: Bearer $token" \
    "http://localhost:8000/admin/users/$user_id/activate")

  echo "$result"
}

deactivate_user() {
  # Usage: be deactivate_user <username>
  local username="$1"

  if [ -z "$username" ]; then
    echo "Usage: $0 deactivate_user <username>"
    return 1
  fi

  # Get admin token
  local token
  token=$(get_admin_token)
  if [ -z "$token" ]; then
    echo "Error: Cannot get admin token for CLI operations"
    return 1
  fi

  # Get user ID first
  local user_id
  user_id=$(get_user_id "$username")
  if [ -z "$user_id" ]; then
    echo "Error: User '$username' not found"
    return 1
  fi

  echo "Deactivating user '$username' (ID: $user_id)..."
  local result
  result=$(podman exec "${GO_BACKEND_CONTAINER_NAME}" curl -sS -X PATCH \
    -H "Authorization: Bearer $token" \
    "http://localhost:8000/admin/users/$user_id/deactivate")

  echo "$result"
}

list_users() {
  # Usage: be list_users
  echo "Listing all users..."

  if ! podman ps --filter name="${GO_BACKEND_CONTAINER_NAME}" --format '{{.Names}}' | grep -q "."; then
    echo "Error: backend container '${GO_BACKEND_CONTAINER_NAME}' is not running."
    return 1
  fi

  # Get admin token
  local token
  token=$(get_admin_token)
  if [ -z "$token" ]; then
    echo "Error: Cannot get admin token for CLI operations"
    return 1
  fi

  local user_list
  user_list=$(podman exec "${GO_BACKEND_CONTAINER_NAME}" curl -sS \
    -H "Authorization: Bearer $token" \
    "http://localhost:8000/admin/users")

  if [ $? -eq 0 ]; then
    echo "$user_list" | sed 's/},{/\n/g' | sed 's/\[{//g' | sed 's/}\]//g' | while IFS= read -r user; do
      if [ -n "$user" ]; then
        id=$(echo "$user" | grep -o '"id":[0-9]*' | cut -d':' -f2)
        username=$(echo "$user" | grep -o '"username":"[^"]*"' | cut -d'"' -f4)
        is_active=$(echo "$user" | grep -o '"is_active":[^,]*' | cut -d':' -f2)
        is_staff=$(echo "$user" | grep -o '"is_staff":[^,]*' | cut -d':' -f2)
        is_superuser=$(echo "$user" | grep -o '"is_superuser":[^,]*' | cut -d':' -f2)
        date_joined=$(echo "$user" | grep -o '"date_joined":"[^"]*"' | cut -d'"' -f4)
        echo "ID: $id | Username: $username | Active: $is_active | Staff: $is_staff | Superuser: $is_superuser | Joined: $date_joined"
      fi
    done
  else
    echo "Failed to fetch user list"
  fi
}

grant_admin() {
  # Usage: be grant_admin <username>
  local username="$1"

  if [ -z "$username" ]; then
    echo "Usage: $0 grant_admin <username>"
    return 1
  fi

  # Get admin token
  local token
  token=$(get_admin_token)
  if [ -z "$token" ]; then
    echo "Error: Cannot get admin token for CLI operations"
    return 1
  fi

  # Get user ID first
  local user_id
  user_id=$(get_user_id "$username")
  if [ -z "$user_id" ]; then
    echo "Error: User '$username' not found"
    return 1
  fi

  echo "Granting admin privileges to user '$username' (ID: $user_id)..."
  local result
  result=$(podman exec "${GO_BACKEND_CONTAINER_NAME}" curl -sS -X PATCH \
    -H "Authorization: Bearer $token" \
    "http://localhost:8000/admin/users/$user_id/grant-admin")

  echo "$result"
}

revoke_admin() {
  # Usage: be revoke_admin <username>
  local username="$1"

  if [ -z "$username" ]; then
    echo "Usage: $0 revoke_admin <username>"
    return 1
  fi

  # Get admin token
  local token
  token=$(get_admin_token)
  if [ -z "$token" ]; then
    echo "Error: Cannot get admin token for CLI operations"
    return 1
  fi

  # Get user ID first
  local user_id
  user_id=$(get_user_id "$username")
  if [ -z "$user_id" ]; then
    echo "Error: User '$username' not found"
    return 1
  fi

  echo "Revoking admin privileges from user '$username' (ID: $user_id)..."
  local result
  result=$(podman exec "${GO_BACKEND_CONTAINER_NAME}" curl -sS -X PATCH \
    -H "Authorization: Bearer $token" \
    "http://localhost:8000/admin/users/$user_id/revoke-admin")

  echo "$result"
}

reset_password() {
  # Usage: be reset_password <username> <new_password>
  local username="$1"
  local new_password="$2"

  if [ -z "$username" ] || [ -z "$new_password" ]; then
    echo "Usage: $0 reset_password <username> <new_password>"
    return 1
  fi

  # Get user ID first
  local user_id
  user_id=$(get_user_id "$username")
  if [ -z "$user_id" ]; then
    echo "Error: User '$username' not found"
    return 1
  fi

  echo "Resetting password for user '$username' (ID: $user_id)..."
  echo "Note: This functionality requires direct database access."
  echo "For now, please use the admin interface or recreate the user."
}

delete_user() {
  # Usage: be delete_user <username>
  local username="$1"

  if [ -z "$username" ]; then
    echo "Usage: $0 delete_user <username>"
    return 1
  fi

  # Get user ID first
  local user_id
  user_id=$(get_user_id "$username")
  if [ -z "$user_id" ]; then
    echo "Error: User '$username' not found"
    return 1
  fi

  local url="http://127.0.0.1:8080/admin/users/$user_id"
  echo "Deleting user '$username' (ID: $user_id)..."
  echo "Are you sure? This action cannot be undone. (Press Ctrl+C to cancel)"
  read -r

  podman exec -it "${INTERACT_CONTAINER_NAME}" sh -lc '
    set -e
    if ! command -v curl >/dev/null 2>&1; then
      apk update >/dev/null 2>&1 || true
      apk add --no-cache curl >/dev/null 2>&1 || true
    fi
    curl -sS -X DELETE "$URL"
  ' \
  -e URL="$url"
}

get_admin_token() {
  # Helper function to get admin token for CLI operations
  # This assumes there's at least one admin user we can use
  local admin_user="testadmin"  # Default admin user
  local admin_pass="testpassword123"  # Default admin password

  # Try to login and get token
  local login_response
  login_response=$(podman exec "${GO_BACKEND_CONTAINER_NAME}" curl -sS -X POST \
    -H "Content-Type: application/json" \
    -d "{\"username\":\"$admin_user\",\"password\":\"$admin_pass\"}" \
    "http://localhost:8000/auth/login" 2>/dev/null)

  if [ $? -eq 0 ] && echo "$login_response" | grep -q "access_token"; then
    echo "$login_response" | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4
  else
    echo ""
  fi
}

get_user_id() {
  # Helper function to get user ID by username
  local username="$1"
  local token
  token=$(get_admin_token)

  if [ -z "$token" ]; then
    echo "Error: Cannot get admin token for CLI operations" >&2
    return 1
  fi

  local user_list
  user_list=$(podman exec "${GO_BACKEND_CONTAINER_NAME}" curl -sS \
    -H "Authorization: Bearer $token" \
    "http://localhost:8000/admin/users" 2>/dev/null)

  if [ $? -eq 0 ]; then
    # Extract user ID using grep and cut (since jq might not be available)
    echo "$user_list" | grep -o "\"id\":[0-9]*,\"username\":\"$username\"" | head -1 | grep -o "\"id\":[0-9]*" | cut -d':' -f2
  else
    echo ""
  fi
}


backup_db() {
  echo "Creating database backup..."
  backup_file="$SUPPORT_DIR/backups/db_backup_$(date +%Y%m%d_%H%M%S).sql"
  mkdir -p "$SUPPORT_DIR/backups"

  podman exec -it "$POSTGRES_CONTAINER_NAME" pg_dump -U "$POSTGRES_USER" "$POSTGRES_DB" >"$backup_file"
  echo "Database backup created: $backup_file"
}

# Check if the command exists as a function
if [ "$COMMAND" = "run_cmd" ]; then
  # If command is run_cmd, pass all remaining arguments to it
  shift 2 # Remove APP_NAME and COMMAND
  run_cmd "$@"
elif [ "$(type -t "$COMMAND")" = "function" ]; then
  # Execute other commands (pass through args)
  shift 1
  "$COMMAND" "$@"
else
  echo "Error: Unknown command '$COMMAND'"
  echo "Available commands:"
  echo "  init        - Initialize project structure"
  echo "  start       - Start all services"
  echo "  stop        - Stop all services"
  echo "  restart     - Restart all services"
  echo "  rebuild     - Rebuild Go image and restart backend"
  echo "  cek         - Check and restart failed containers"
  echo "  status      - Show pod and container status"
  echo "  logs        - Show container logs"
  echo "  shell       - Open shell in container"
  echo "  test_api    - Test API endpoints"
  echo "  backup_db   - Create database backup"
  echo "  pg          - Start pgAdmin"
  echo "  register_user - Register a new user (usage: be register_user <username> <password> [role])"
  echo "  bootstrap_admin - Create the first super admin user (usage: be bootstrap_admin <username> <password>)"
  echo "  activate_user - Activate an inactive user (usage: be activate_user <username>)"
  echo "  deactivate_user - Deactivate an active user (usage: be deactivate_user <username>)"
  echo "  list_users  - List all users with their status"
  echo "  grant_admin - Grant admin privileges (usage: be grant_admin <username>)"
  echo "  revoke_admin - Revoke admin privileges (usage: be revoke_admin <username>)"
  echo "  reset_password - Reset user password (usage: be reset_password <username> <new_password>)"
  echo "  delete_user - Delete user account (usage: be delete_user <username>)"
  echo "  run_cmd     - Run command in interactive container"
  exit 1
fi
