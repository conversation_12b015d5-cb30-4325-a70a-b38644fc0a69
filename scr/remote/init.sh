#!/bin/bash

# This script installs the necessary packages and configures the script for Vultr VPS to create environment for BESB
# Exit on error
set -e

#---------------------------------------------------------------------
# ONLINE PRE-REQUISITES: Install all necessary packages first
#---------------------------------------------------------------------
echo "[INFO] Refreshing keyring & package databases…"
sudo pacman -Syy --noconfirm --needed archlinux-keyring

echo "[INFO] Upgrading system and installing packages…"
sudo pacman -Suu --noconfirm podman git zsh htop rsync tmux

echo "[INFO] Changing default shell to zsh..."
chsh -s /usr/bin/zsh loomino

#---------------------------------------------------------------------
# ONLINE PROCESS: Network-dependent configuration and service activation
#---------------------------------------------------------------------
echo "[INFO] Installing oh-my-zsh for loomino..."
sudo -u loomino sh -c "wget https://raw.githubusercontent.com/ohmyzsh/ohmyzsh/master/tools/install.sh -O /home/<USER>/install.sh"
chmod +x /home/<USER>/install.sh
rm -rfv /home/<USER>/.oh-my-zsh
sudo -u loomino -i sh -c "RUNZSH=no CHSH=no HOME=/home/<USER>/home/<USER>/install.sh"

echo "[INFO] Setting oh-my-zsh theme to 'evan'..."
sudo -u loomino sed -i 's/^ZSH_THEME=.*/ZSH_THEME=\"evan\"/' /home/<USER>/.zshrc
