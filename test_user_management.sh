#!/bin/bash

# Test script for user management system
set -e

echo "=== User Management System Test ==="
echo

# Test 1: Register first admin user (should be auto-activated)
echo "1. Testing admin user registration..."
ADMIN_RESPONSE=$(podman exec GNRS_go_backend wget -qO- --post-data='{"username":"admin1","password":"admin123","role":"admin"}' --header='Content-Type: application/json' http://localhost:8000/auth/register)
echo "Admin registration response: $ADMIN_RESPONSE"

# Extract admin user ID
ADMIN_ID=$(echo "$ADMIN_RESPONSE" | grep -o '"id":[0-9]*' | cut -d':' -f2)
echo "Admin user ID: $ADMIN_ID"
echo

# Test 2: Login as admin
echo "2. Testing admin login..."
LOGIN_RESPONSE=$(podman exec GNRS_go_backend wget -qO- --post-data='{"username":"admin1","password":"admin123"}' --header='Content-Type: application/json' http://localhost:8000/auth/login)
echo "Login response: $LOGIN_RESPONSE"

# Extract access token
ACCESS_TOKEN=$(echo "$LOGIN_RESPONSE" | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)
echo "Access token: ${ACCESS_TOKEN:0:50}..."
echo

# Test 3: Register regular user (should be inactive)
echo "3. Testing regular user registration..."
USER_RESPONSE=$(podman exec GNRS_go_backend wget -qO- --post-data='{"username":"user1","password":"user123","role":"user"}' --header='Content-Type: application/json' http://localhost:8000/auth/register)
echo "User registration response: $USER_RESPONSE"

# Extract user ID
USER_ID=$(echo "$USER_RESPONSE" | grep -o '"id":[0-9]*' | cut -d':' -f2)
echo "Regular user ID: $USER_ID"
echo

# Test 4: Try to login as inactive user (should fail)
echo "4. Testing inactive user login (should fail)..."
INACTIVE_LOGIN=$(podman exec GNRS_go_backend wget -qO- --post-data='{"username":"user1","password":"user123"}' --header='Content-Type: application/json' http://localhost:8000/auth/login 2>&1 || echo "Login failed as expected")
echo "Inactive user login result: $INACTIVE_LOGIN"
echo

# Test 5: List all users as admin
echo "5. Testing user list endpoint..."
USERS_LIST=$(podman exec GNRS_go_backend wget -qO- --header="Authorization: Bearer $ACCESS_TOKEN" http://localhost:8000/admin/users)
echo "Users list: $USERS_LIST"
echo

# Test 6: Activate user
echo "6. Testing user activation..."
ACTIVATE_RESPONSE=$(podman exec GNRS_go_backend wget -qO- --method=PATCH --header="Authorization: Bearer $ACCESS_TOKEN" http://localhost:8000/admin/users/$USER_ID/activate)
echo "Activation response: $ACTIVATE_RESPONSE"
echo

# Test 7: Login as activated user (should work now)
echo "7. Testing activated user login..."
ACTIVE_LOGIN=$(podman exec GNRS_go_backend wget -qO- --post-data='{"username":"user1","password":"user123"}' --header='Content-Type: application/json' http://localhost:8000/auth/login)
echo "Activated user login: $ACTIVE_LOGIN"
echo

# Test 8: Grant admin privileges
echo "8. Testing grant admin privileges..."
GRANT_ADMIN_RESPONSE=$(podman exec GNRS_go_backend wget -qO- --method=PATCH --header="Authorization: Bearer $ACCESS_TOKEN" http://localhost:8000/admin/users/$USER_ID/grant-admin)
echo "Grant admin response: $GRANT_ADMIN_RESPONSE"
echo

# Test 9: Verify user is now admin
echo "9. Verifying user is now admin..."
UPDATED_USERS_LIST=$(podman exec GNRS_go_backend wget -qO- --header="Authorization: Bearer $ACCESS_TOKEN" http://localhost:8000/admin/users)
echo "Updated users list: $UPDATED_USERS_LIST"
echo

# Test 10: Revoke admin privileges
echo "10. Testing revoke admin privileges..."
REVOKE_ADMIN_RESPONSE=$(podman exec GNRS_go_backend wget -qO- --method=PATCH --header="Authorization: Bearer $ACCESS_TOKEN" http://localhost:8000/admin/users/$USER_ID/revoke-admin)
echo "Revoke admin response: $REVOKE_ADMIN_RESPONSE"
echo

# Test 11: Deactivate user
echo "11. Testing user deactivation..."
DEACTIVATE_RESPONSE=$(podman exec GNRS_go_backend wget -qO- --method=PATCH --header="Authorization: Bearer $ACCESS_TOKEN" http://localhost:8000/admin/users/$USER_ID/deactivate)
echo "Deactivation response: $DEACTIVATE_RESPONSE"
echo

# Test 12: Final user list
echo "12. Final user list..."
FINAL_USERS_LIST=$(podman exec GNRS_go_backend wget -qO- --header="Authorization: Bearer $ACCESS_TOKEN" http://localhost:8000/admin/users)
echo "Final users list: $FINAL_USERS_LIST"
echo

echo "=== Test completed successfully! ==="
echo
echo "Summary:"
echo "- Admin user registration: ✓"
echo "- Admin login: ✓"
echo "- Regular user registration (inactive): ✓"
echo "- Inactive user login blocked: ✓"
echo "- User list endpoint: ✓"
echo "- User activation: ✓"
echo "- Activated user login: ✓"
echo "- Grant admin privileges: ✓"
echo "- Revoke admin privileges: ✓"
echo "- User deactivation: ✓"
echo
echo "All user management features are working correctly!"
